/**
 * UI交互模块
 * 处理所有用户界面交互
 */

class UIManager {
    constructor() {
        this.currentPage = 'home';
        this.loadingCount = 0;
        this.toastContainer = null;
        this.modal = null;
        this.searchContainer = null;
        
        this.init();
    }

    /**
     * 初始化UI管理器
     */
    init() {
        this.toastContainer = document.getElementById('toast-container');
        this.modal = document.getElementById('modal');
        this.searchContainer = document.getElementById('search-container');
        
        this.bindEvents();
        this.hideLoading();
    }

    /**
     * 绑定事件
     */
    bindEvents() {
        // 导航事件
        document.addEventListener('click', (e) => {
            if (e.target.matches('[data-page]')) {
                e.preventDefault();
                const page = e.target.getAttribute('data-page');
                this.showPage(page);
            }
        });

        // 搜索事件
        const searchBtn = document.getElementById('search-btn');
        const searchClose = document.getElementById('search-close');
        const searchSubmit = document.getElementById('search-submit');
        const searchInput = document.getElementById('search-input');

        if (searchBtn) {
            searchBtn.addEventListener('click', () => this.toggleSearch());
        }

        if (searchClose) {
            searchClose.addEventListener('click', () => this.hideSearch());
        }

        if (searchSubmit) {
            searchSubmit.addEventListener('click', () => this.performSearch());
        }

        if (searchInput) {
            searchInput.addEventListener('keypress', (e) => {
                if (e.key === 'Enter') {
                    this.performSearch();
                }
            });
        }

        // 模态框事件
        if (this.modal) {
            const modalClose = document.getElementById('modal-close');
            if (modalClose) {
                modalClose.addEventListener('click', () => this.hideModal());
            }

            this.modal.addEventListener('click', (e) => {
                if (e.target === this.modal) {
                    this.hideModal();
                }
            });
        }

        // ESC键关闭模态框和搜索
        document.addEventListener('keydown', (e) => {
            if (e.key === 'Escape') {
                this.hideModal();
                this.hideSearch();
            }
        });
    }

    /**
     * 显示页面
     * @param {string} pageName - 页面名称
     */
    showPage(pageName) {
        // 隐藏所有页面
        const pages = document.querySelectorAll('.page');
        pages.forEach(page => {
            page.classList.remove('active');
        });

        // 显示目标页面
        const targetPage = document.getElementById(`page-${pageName}`);
        if (targetPage) {
            targetPage.classList.add('active');
            this.currentPage = pageName;
        }

        // 更新导航状态
        const navLinks = document.querySelectorAll('.nav-link');
        navLinks.forEach(link => {
            link.classList.remove('active');
            if (link.getAttribute('data-page') === pageName) {
                link.classList.add('active');
            }
        });

        // 滚动到顶部
        window.scrollTo({ top: 0, behavior: 'smooth' });
    }

    /**
     * 显示加载状态
     * @param {string} message - 加载消息
     */
    showLoading(message = '加载中...') {
        this.loadingCount++;
        const loadingScreen = document.getElementById('loading-screen');
        if (loadingScreen) {
            const loadingText = loadingScreen.querySelector('p');
            if (loadingText) {
                loadingText.textContent = message;
            }
            loadingScreen.classList.remove('hidden');
        }
    }

    /**
     * 隐藏加载状态
     */
    hideLoading() {
        this.loadingCount = Math.max(0, this.loadingCount - 1);
        if (this.loadingCount === 0) {
            const loadingScreen = document.getElementById('loading-screen');
            if (loadingScreen) {
                setTimeout(() => {
                    loadingScreen.classList.add('hidden');
                }, UI_CONFIG.LOADING_MIN_TIME);
            }
        }
    }

    /**
     * 显示Toast通知
     * @param {string} message - 消息内容
     * @param {string} type - 消息类型 (success, error, warning, info)
     * @param {number} duration - 显示时长
     */
    showToast(message, type = 'info', duration = UI_CONFIG.TOAST_DURATION) {
        if (!this.toastContainer) return;

        const toast = document.createElement('div');
        toast.className = `toast ${type}`;
        
        const icons = {
            success: '✅',
            error: '❌',
            warning: '⚠️',
            info: 'ℹ️'
        };

        toast.innerHTML = `
            <div class="toast-content">
                <span class="toast-icon">${icons[type] || icons.info}</span>
                <span class="toast-message">${Utils.escapeHtml(message)}</span>
            </div>
        `;

        this.toastContainer.appendChild(toast);

        // 显示动画
        setTimeout(() => {
            toast.classList.add('show');
        }, 10);

        // 自动隐藏
        setTimeout(() => {
            toast.classList.remove('show');
            setTimeout(() => {
                if (toast.parentNode) {
                    toast.parentNode.removeChild(toast);
                }
            }, 300);
        }, duration);
    }

    /**
     * 显示模态框
     * @param {string} title - 标题
     * @param {string} content - 内容
     * @param {Array} buttons - 按钮配置
     */
    showModal(title, content, buttons = []) {
        if (!this.modal) return;

        const modalTitle = document.getElementById('modal-title');
        const modalBody = document.getElementById('modal-body');
        const modalFooter = document.getElementById('modal-footer');

        if (modalTitle) modalTitle.textContent = title;
        if (modalBody) modalBody.innerHTML = content;
        
        if (modalFooter) {
            modalFooter.innerHTML = '';
            buttons.forEach(button => {
                const btn = document.createElement('button');
                btn.className = `btn ${button.class || 'btn-primary'}`;
                btn.textContent = button.text;
                btn.addEventListener('click', () => {
                    if (button.handler) {
                        button.handler();
                    }
                    if (button.closeModal !== false) {
                        this.hideModal();
                    }
                });
                modalFooter.appendChild(btn);
            });
        }

        this.modal.classList.add('active');
        document.body.style.overflow = 'hidden';
    }

    /**
     * 隐藏模态框
     */
    hideModal() {
        if (!this.modal) return;
        
        this.modal.classList.remove('active');
        document.body.style.overflow = '';
    }

    /**
     * 显示确认对话框
     * @param {string} message - 确认消息
     * @param {Function} onConfirm - 确认回调
     * @param {Function} onCancel - 取消回调
     */
    showConfirm(message, onConfirm, onCancel) {
        this.showModal('确认', `<p>${Utils.escapeHtml(message)}</p>`, [
            {
                text: '取消',
                class: 'btn-ghost',
                handler: onCancel
            },
            {
                text: '确认',
                class: 'btn-primary',
                handler: onConfirm
            }
        ]);
    }

    /**
     * 显示搜索栏
     */
    showSearch() {
        if (this.searchContainer) {
            this.searchContainer.classList.add('active');
            const searchInput = document.getElementById('search-input');
            if (searchInput) {
                setTimeout(() => searchInput.focus(), 100);
            }
        }
    }

    /**
     * 隐藏搜索栏
     */
    hideSearch() {
        if (this.searchContainer) {
            this.searchContainer.classList.remove('active');
            const searchInput = document.getElementById('search-input');
            if (searchInput) {
                searchInput.value = '';
            }
        }
    }

    /**
     * 切换搜索栏显示状态
     */
    toggleSearch() {
        if (this.searchContainer && this.searchContainer.classList.contains('active')) {
            this.hideSearch();
        } else {
            this.showSearch();
        }
    }

    /**
     * 执行搜索
     */
    async performSearch() {
        const searchInput = document.getElementById('search-input');
        if (!searchInput) return;

        const keyword = searchInput.value.trim();
        if (!keyword) {
            this.showToast('请输入搜索关键词', 'warning');
            return;
        }

        try {
            this.showLoading('搜索中...');
            const results = await apiService.search(keyword);
            
            // 显示搜索结果
            this.showSearchResults(keyword, results);
            this.hideSearch();
            
        } catch (error) {
            console.error('搜索失败:', error);
            this.showToast('搜索失败，请稍后重试', 'error');
        } finally {
            this.hideLoading();
        }
    }

    /**
     * 显示搜索结果
     * @param {string} keyword - 搜索关键词
     * @param {Object} results - 搜索结果
     */
    showSearchResults(keyword, results) {
        const content = `
            <div class="search-results">
                <h3>搜索结果：${Utils.escapeHtml(keyword)}</h3>
                
                ${results.shops && results.shops.length > 0 ? `
                    <div class="search-section">
                        <h4>商店 (${results.shops.length})</h4>
                        <div class="search-items">
                            ${results.shops.map(shop => `
                                <div class="search-item" onclick="app.showShopDetail('${shop.merchant_id}')">
                                    <div class="search-item-avatar">${Utils.getShopAvatar(shop.shop_name)}</div>
                                    <div class="search-item-info">
                                        <div class="search-item-title">${Utils.escapeHtml(shop.shop_name)}</div>
                                        <div class="search-item-desc">${Utils.escapeHtml(shop.shop_description)}</div>
                                    </div>
                                </div>
                            `).join('')}
                        </div>
                    </div>
                ` : ''}
                
                ${results.products && results.products.length > 0 ? `
                    <div class="search-section">
                        <h4>商品 (${results.products.length})</h4>
                        <div class="search-items">
                            ${results.products.map(product => `
                                <div class="search-item" onclick="app.showProductDetail('${product.product_id}')">
                                    <div class="search-item-avatar">📦</div>
                                    <div class="search-item-info">
                                        <div class="search-item-title">${Utils.escapeHtml(product.product_name)}</div>
                                        <div class="search-item-desc">¥${Utils.formatPrice(product.product_price)}</div>
                                    </div>
                                </div>
                            `).join('')}
                        </div>
                    </div>
                ` : ''}
                
                ${(!results.shops || results.shops.length === 0) && (!results.products || results.products.length === 0) ? `
                    <div class="no-results">
                        <p>未找到相关结果</p>
                        <p>请尝试其他关键词</p>
                    </div>
                ` : ''}
            </div>
        `;

        this.showModal('搜索结果', content, [
            {
                text: '关闭',
                class: 'btn-ghost'
            }
        ]);
    }

    /**
     * 创建空状态显示
     * @param {string} message - 空状态消息
     * @param {string} icon - 图标
     * @returns {string} HTML字符串
     */
    createEmptyState(message, icon = '📭') {
        return `
            <div class="empty-state">
                <div class="empty-icon">${icon}</div>
                <p class="empty-message">${Utils.escapeHtml(message)}</p>
            </div>
        `;
    }

    /**
     * 创建错误状态显示
     * @param {string} message - 错误消息
     * @param {Function} retryHandler - 重试处理函数
     * @returns {string} HTML字符串
     */
    createErrorState(message, retryHandler = null) {
        const retryButton = retryHandler ? `
            <button class="btn btn-outline" onclick="(${retryHandler.toString()})()">
                重试
            </button>
        ` : '';

        return `
            <div class="error-state">
                <div class="error-icon">❌</div>
                <p class="error-message">${Utils.escapeHtml(message)}</p>
                ${retryButton}
            </div>
        `;
    }
}

// 创建全局UI管理器实例
const uiManager = new UIManager();
