/**
 * 响应式样式文件
 * 适配PC和移动端
 */

/* 平板设备 (768px - 1024px) */
@media (max-width: 1024px) {
    .container {
        padding: 0 var(--spacing-lg);
    }
    
    .hero-content h2 {
        font-size: var(--font-size-2xl);
    }
    
    .shops-grid {
        grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
    }
    
    .products-grid {
        grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
    }
    
    .product-detail-header {
        grid-template-columns: 1fr;
        gap: var(--spacing-xl);
    }
    
    .product-detail-image {
        height: 300px;
    }
    
    .shop-detail {
        flex-direction: column;
        text-align: center;
        gap: var(--spacing-lg);
    }
}

/* 移动设备 (最大768px) */
@media (max-width: 768px) {
    :root {
        --font-size-3xl: 28px;
        --font-size-2xl: 22px;
        --font-size-xl: 18px;
        --spacing-2xl: 32px;
        --spacing-xl: 24px;
    }
    
    .container {
        padding: 0 var(--spacing-md);
    }
    
    /* Header移动端适配 */
    .header-content {
        padding: var(--spacing-sm) 0;
    }
    
    .logo h1 {
        font-size: var(--font-size-lg);
    }
    
    .nav {
        display: none;
    }
    
    .nav.mobile-nav {
        display: flex;
        position: fixed;
        top: 60px;
        left: 0;
        right: 0;
        background: var(--background-primary);
        border-bottom: 1px solid var(--background-secondary);
        padding: var(--spacing-md);
        flex-direction: column;
        gap: var(--spacing-sm);
        z-index: 999;
        transform: translateY(-100%);
        transition: transform var(--transition-base);
    }
    
    .nav.mobile-nav.active {
        transform: translateY(0);
    }
    
    .mobile-menu-btn {
        display: block;
        background: none;
        border: none;
        font-size: var(--font-size-lg);
        color: var(--text-secondary);
        cursor: pointer;
        padding: var(--spacing-sm);
    }
    
    /* 搜索栏移动端适配 */
    .search-container {
        top: 60px;
    }
    
    .search-bar {
        flex-direction: column;
        gap: var(--spacing-sm);
    }
    
    .search-bar input {
        width: 100%;
    }
    
    .search-submit {
        width: 100%;
    }
    
    /* Hero区域移动端适配 */
    .hero {
        padding: var(--spacing-xl) 0;
    }
    
    .hero-content h2 {
        font-size: var(--font-size-2xl);
    }
    
    .hero-content p {
        font-size: var(--font-size-base);
    }
    
    .hero-actions {
        flex-direction: column;
        align-items: center;
    }
    
    .hero-actions .btn {
        width: 100%;
        max-width: 280px;
    }
    
    /* 网格布局移动端适配 */
    .shops-grid {
        grid-template-columns: 1fr;
        gap: var(--spacing-md);
    }
    
    .products-grid {
        grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
        gap: var(--spacing-md);
    }
    
    /* 页面头部移动端适配 */
    .page-header {
        flex-direction: column;
        align-items: flex-start;
        gap: var(--spacing-md);
    }
    
    .filters {
        width: 100%;
        justify-content: space-between;
    }
    
    /* 商店卡片移动端适配 */
    .shop-card {
        padding: var(--spacing-md);
    }
    
    .shop-card-header {
        gap: var(--spacing-sm);
    }
    
    .shop-avatar {
        width: 40px;
        height: 40px;
        font-size: var(--font-size-lg);
    }
    
    .shop-stats {
        flex-wrap: wrap;
        gap: var(--spacing-sm);
    }
    
    /* 商品卡片移动端适配 */
    .product-card {
        margin-bottom: var(--spacing-md);
    }
    
    .product-image {
        height: 160px;
        font-size: var(--font-size-2xl);
    }
    
    .product-content {
        padding: var(--spacing-md);
    }
    
    .product-footer {
        flex-direction: column;
        align-items: flex-start;
        gap: var(--spacing-xs);
    }
    
    /* 商店详情移动端适配 */
    .shop-header {
        padding: var(--spacing-xl) 0;
        margin-bottom: var(--spacing-lg);
    }
    
    .shop-detail-avatar {
        width: 60px;
        height: 60px;
        font-size: var(--font-size-2xl);
    }
    
    .shop-detail-info h2 {
        font-size: var(--font-size-2xl);
    }
    
    .shop-detail-actions {
        width: 100%;
        justify-content: center;
    }
    
    .shop-detail-actions .btn {
        flex: 1;
        min-width: 120px;
    }
    
    /* 商品详情移动端适配 */
    .product-detail {
        padding: var(--spacing-lg) 0;
    }
    
    .product-detail-header {
        gap: var(--spacing-lg);
    }
    
    .product-detail-image {
        height: 250px;
        font-size: 3rem;
    }
    
    .product-detail-info h1 {
        font-size: var(--font-size-2xl);
    }
    
    .product-detail-actions {
        flex-direction: column;
    }
    
    .product-detail-actions .btn {
        width: 100%;
    }
    
    /* 订单页面移动端适配 */
    .order-detail {
        padding: var(--spacing-lg) 0;
    }
    
    .order-card {
        padding: var(--spacing-lg);
        margin-bottom: var(--spacing-md);
    }
    
    .order-info-item {
        flex-direction: column;
        align-items: flex-start;
        gap: var(--spacing-xs);
    }
    
    .order-actions {
        flex-direction: column;
    }
    
    .order-actions .btn {
        width: 100%;
    }
    
    /* 支付方式移动端适配 */
    .payment-options {
        grid-template-columns: 1fr;
        gap: var(--spacing-sm);
    }
    
    .payment-option {
        padding: var(--spacing-md);
    }
    
    /* 模态框移动端适配 */
    .modal-content {
        width: 95%;
        max-height: 90vh;
        margin: var(--spacing-md);
    }
    
    .modal-header {
        padding: var(--spacing-md);
    }
    
    .modal-body {
        padding: var(--spacing-md);
    }
    
    .modal-footer {
        padding: var(--spacing-md);
        flex-direction: column;
    }
    
    .modal-footer .btn {
        width: 100%;
    }
    
    /* Toast通知移动端适配 */
    .toast-container {
        top: 10px;
        right: 10px;
        left: 10px;
    }
    
    .toast {
        min-width: auto;
        width: 100%;
    }
    
    /* Footer移动端适配 */
    .footer-content {
        flex-direction: column;
        text-align: center;
        gap: var(--spacing-md);
    }
    
    .footer-links {
        justify-content: center;
        flex-wrap: wrap;
    }
}

/* 小屏幕移动设备 (最大480px) */
@media (max-width: 480px) {
    :root {
        --spacing-2xl: 24px;
        --spacing-xl: 20px;
        --spacing-lg: 16px;
    }
    
    .container {
        padding: 0 var(--spacing-sm);
    }
    
    .hero {
        padding: var(--spacing-lg) 0;
    }
    
    .featured-shops {
        padding: var(--spacing-lg) 0;
    }
    
    .products-grid {
        grid-template-columns: 1fr;
    }
    
    .product-image {
        height: 140px;
    }
    
    .shop-card,
    .product-card,
    .order-card {
        padding: var(--spacing-sm);
    }
    
    .modal-content {
        width: 98%;
        margin: var(--spacing-sm);
    }
}

/* 横屏模式适配 */
@media (max-height: 500px) and (orientation: landscape) {
    .hero {
        padding: var(--spacing-md) 0;
    }
    
    .hero-content h2 {
        font-size: var(--font-size-xl);
        margin-bottom: var(--spacing-sm);
    }
    
    .hero-content p {
        margin-bottom: var(--spacing-md);
    }
    
    .hero-actions {
        flex-direction: row;
        gap: var(--spacing-sm);
    }
    
    .modal-content {
        max-height: 95vh;
    }
}
