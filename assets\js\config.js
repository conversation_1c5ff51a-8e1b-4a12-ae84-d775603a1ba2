/**
 * 应用配置文件
 */

// API配置
const API_CONFIG = {
    BASE_URL: 'http://127.0.0.1:7893/',
    TIMEOUT: 10000,
    ENDPOINTS: {
        GET_MERCHANT_INFO: 'get_merchant_info.php',
        REGISTER_MERCHANT: 'register_merchant.php',
        GET_PRODUCT_LIST: 'get_product_list.php',
        GET_PRODUCT_INFO: 'get_product_info.php',
        CREATE_ORDER: 'create_order.php',
        CHECK_PAYMENT_STATUS: 'check_payment_status.php',
        SET_ORDER_PAYMENT_STATUS: 'set_order_payment_status.php'
    }
};

// 商户秘钥生成配置
const MERCHANT_CONFIG = {
    SECRET_KEY: 'yjsyjs_merchant_secret_key_2024',
    SALT: 'yjsyjs_salt_2024'
};

// UI配置
const UI_CONFIG = {
    ANIMATION_DURATION: 300,
    TOAST_DURATION: 3000,
    LOADING_MIN_TIME: 500,
    DEBOUNCE_DELAY: 300
};

// 支付配置
const PAYMENT_CONFIG = {
    TYPES: {
        WXPAY: 'wxpay',
        ALIPAY: 'alipay'
    },
    NAMES: {
        wxpay: '微信支付',
        alipay: '支付宝'
    }
};

// 应用配置
const APP_CONFIG = {
    NAME: 'Mika 寄售商城',
    VERSION: '1.0.0',
    DESCRIPTION: '匿名、安全的寄售平台，7x24h自动发货',
    ADMIN_URL: 'https://cloudshop.qnm6.top/admin/index.php',
    COMPLAINT_URL: 'https://cloudshop.qnm6.top/tousu.html',
    TELEGRAM_CHANNEL: '@MikaJiShou8',
    BOT_USERNAME: 'MikaJishouBot'
};

// 默认数据
const DEFAULT_DATA = {
    SHOP: {
        name: '未知商店',
        description: '暂无介绍'
    },
    PRODUCT: {
        name: '未知商品',
        description: '暂无描述',
        price: '0.00',
        stock: 0
    }
};

// 错误消息
const ERROR_MESSAGES = {
    NETWORK_ERROR: '网络连接失败，请检查网络设置',
    API_ERROR: 'API调用失败，请稍后重试',
    MERCHANT_NOT_FOUND: '商户不存在或获取商户信息失败',
    PRODUCT_NOT_FOUND: '商品不存在或获取商品信息失败',
    ORDER_CREATE_FAILED: '创建订单失败，请稍后重试',
    PAYMENT_CHECK_FAILED: '检查支付状态失败',
    OUT_OF_STOCK: '商品已售罄，无法购买',
    INVALID_PARAMS: '参数错误'
};

// 成功消息
const SUCCESS_MESSAGES = {
    ORDER_CREATED: '订单创建成功！',
    PAYMENT_SUCCESS: '支付成功！',
    COPY_SUCCESS: '复制成功！',
    SHARE_SUCCESS: '分享链接已生成！'
};

// 导出配置（如果支持ES6模块）
if (typeof module !== 'undefined' && module.exports) {
    module.exports = {
        API_CONFIG,
        MERCHANT_CONFIG,
        UI_CONFIG,
        PAYMENT_CONFIG,
        APP_CONFIG,
        DEFAULT_DATA,
        ERROR_MESSAGES,
        SUCCESS_MESSAGES
    };
}
