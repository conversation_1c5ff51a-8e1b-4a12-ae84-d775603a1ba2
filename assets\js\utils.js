/**
 * 工具函数模块
 * 提供各种实用的工具函数
 */

class Utils {
    /**
     * 防抖函数
     * @param {Function} func - 要防抖的函数
     * @param {number} delay - 延迟时间（毫秒）
     * @returns {Function} 防抖后的函数
     */
    static debounce(func, delay = UI_CONFIG.DEBOUNCE_DELAY) {
        let timeoutId;
        return function (...args) {
            clearTimeout(timeoutId);
            timeoutId = setTimeout(() => func.apply(this, args), delay);
        };
    }

    /**
     * 节流函数
     * @param {Function} func - 要节流的函数
     * @param {number} delay - 延迟时间（毫秒）
     * @returns {Function} 节流后的函数
     */
    static throttle(func, delay = UI_CONFIG.DEBOUNCE_DELAY) {
        let lastCall = 0;
        return function (...args) {
            const now = Date.now();
            if (now - lastCall >= delay) {
                lastCall = now;
                return func.apply(this, args);
            }
        };
    }

    /**
     * 格式化价格
     * @param {string|number} price - 价格
     * @returns {string} 格式化后的价格
     */
    static formatPrice(price) {
        if (!price) return '0.00';
        const num = parseFloat(price);
        return isNaN(num) ? '0.00' : num.toFixed(2);
    }

    /**
     * 格式化日期
     * @param {string|Date} date - 日期
     * @returns {string} 格式化后的日期
     */
    static formatDate(date) {
        if (!date) return '';
        
        const d = new Date(date);
        if (isNaN(d.getTime())) return '';
        
        const year = d.getFullYear();
        const month = String(d.getMonth() + 1).padStart(2, '0');
        const day = String(d.getDate()).padStart(2, '0');
        const hours = String(d.getHours()).padStart(2, '0');
        const minutes = String(d.getMinutes()).padStart(2, '0');
        
        return `${year}-${month}-${day} ${hours}:${minutes}`;
    }

    /**
     * 生成随机ID
     * @param {number} length - ID长度
     * @returns {string} 随机ID
     */
    static generateId(length = 8) {
        const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789';
        let result = '';
        for (let i = 0; i < length; i++) {
            result += chars.charAt(Math.floor(Math.random() * chars.length));
        }
        return result;
    }

    /**
     * 复制文本到剪贴板
     * @param {string} text - 要复制的文本
     * @returns {Promise<boolean>} 是否复制成功
     */
    static async copyToClipboard(text) {
        try {
            if (navigator.clipboard && window.isSecureContext) {
                await navigator.clipboard.writeText(text);
                return true;
            } else {
                // 降级方案
                const textArea = document.createElement('textarea');
                textArea.value = text;
                textArea.style.position = 'fixed';
                textArea.style.left = '-999999px';
                textArea.style.top = '-999999px';
                document.body.appendChild(textArea);
                textArea.focus();
                textArea.select();
                
                const success = document.execCommand('copy');
                document.body.removeChild(textArea);
                return success;
            }
        } catch (error) {
            console.error('复制失败:', error);
            return false;
        }
    }

    /**
     * 获取URL参数
     * @param {string} name - 参数名
     * @returns {string|null} 参数值
     */
    static getUrlParam(name) {
        const urlParams = new URLSearchParams(window.location.search);
        return urlParams.get(name);
    }

    /**
     * 设置URL参数
     * @param {string} name - 参数名
     * @param {string} value - 参数值
     */
    static setUrlParam(name, value) {
        const url = new URL(window.location);
        url.searchParams.set(name, value);
        window.history.pushState({}, '', url);
    }

    /**
     * 移除URL参数
     * @param {string} name - 参数名
     */
    static removeUrlParam(name) {
        const url = new URL(window.location);
        url.searchParams.delete(name);
        window.history.pushState({}, '', url);
    }

    /**
     * 验证邮箱格式
     * @param {string} email - 邮箱地址
     * @returns {boolean} 是否有效
     */
    static isValidEmail(email) {
        const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        return emailRegex.test(email);
    }

    /**
     * 验证手机号格式
     * @param {string} phone - 手机号
     * @returns {boolean} 是否有效
     */
    static isValidPhone(phone) {
        const phoneRegex = /^1[3-9]\d{9}$/;
        return phoneRegex.test(phone);
    }

    /**
     * 截断文本
     * @param {string} text - 原文本
     * @param {number} maxLength - 最大长度
     * @param {string} suffix - 后缀
     * @returns {string} 截断后的文本
     */
    static truncateText(text, maxLength = 50, suffix = '...') {
        if (!text || text.length <= maxLength) return text || '';
        return text.substring(0, maxLength - suffix.length) + suffix;
    }

    /**
     * 转义HTML
     * @param {string} text - 原文本
     * @returns {string} 转义后的文本
     */
    static escapeHtml(text) {
        const div = document.createElement('div');
        div.textContent = text;
        return div.innerHTML;
    }

    /**
     * 获取商店头像文字
     * @param {string} shopName - 商店名称
     * @returns {string} 头像文字
     */
    static getShopAvatar(shopName) {
        if (!shopName) return '?';
        
        // 提取中文字符
        const chineseChars = shopName.match(/[\u4e00-\u9fa5]/g);
        if (chineseChars && chineseChars.length > 0) {
            return chineseChars[0];
        }
        
        // 提取英文字符
        const englishChars = shopName.match(/[a-zA-Z]/g);
        if (englishChars && englishChars.length > 0) {
            return englishChars[0].toUpperCase();
        }
        
        // 提取数字
        const numbers = shopName.match(/[0-9]/g);
        if (numbers && numbers.length > 0) {
            return numbers[0];
        }
        
        return '?';
    }

    /**
     * 获取库存状态
     * @param {number} stock - 库存数量
     * @returns {Object} 库存状态信息
     */
    static getStockStatus(stock) {
        const quantity = parseInt(stock) || 0;
        
        if (quantity <= 0) {
            return {
                status: 'out-of-stock',
                text: '已售罄',
                class: 'out-of-stock'
            };
        } else if (quantity <= 5) {
            return {
                status: 'low-stock',
                text: `仅剩 ${quantity} 件`,
                class: 'low-stock'
            };
        } else {
            return {
                status: 'in-stock',
                text: `库存 ${quantity} 件`,
                class: 'in-stock'
            };
        }
    }

    /**
     * 生成分享链接
     * @param {string} type - 分享类型 (shop/product)
     * @param {string} id - ID
     * @returns {string} 分享链接
     */
    static generateShareUrl(type, id) {
        const baseUrl = window.location.origin + window.location.pathname;
        
        if (type === 'shop') {
            return `${baseUrl}?shop=${id}`;
        } else if (type === 'product') {
            return `${baseUrl}?product=${id}`;
        }
        
        return baseUrl;
    }

    /**
     * 生成Telegram分享链接
     * @param {string} type - 分享类型
     * @param {string} id - ID
     * @returns {string} Telegram分享链接
     */
    static generateTelegramShareUrl(type, id) {
        const botUsername = APP_CONFIG.BOT_USERNAME;
        
        if (type === 'shop') {
            return `https://t.me/${botUsername}?start=${id}`;
        } else if (type === 'product') {
            return `https://t.me/${botUsername}?start=shoping_${id}`;
        }
        
        return `https://t.me/${botUsername}`;
    }

    /**
     * 检测设备类型
     * @returns {string} 设备类型
     */
    static getDeviceType() {
        const userAgent = navigator.userAgent.toLowerCase();
        
        if (/mobile|android|iphone|ipad|phone/i.test(userAgent)) {
            return 'mobile';
        } else if (/tablet|ipad/i.test(userAgent)) {
            return 'tablet';
        } else {
            return 'desktop';
        }
    }

    /**
     * 检测是否为iOS设备
     * @returns {boolean} 是否为iOS
     */
    static isIOS() {
        return /iPad|iPhone|iPod/.test(navigator.userAgent);
    }

    /**
     * 检测是否为Android设备
     * @returns {boolean} 是否为Android
     */
    static isAndroid() {
        return /Android/.test(navigator.userAgent);
    }

    /**
     * 平滑滚动到元素
     * @param {string|Element} target - 目标元素或选择器
     * @param {number} offset - 偏移量
     */
    static scrollToElement(target, offset = 0) {
        const element = typeof target === 'string' ? document.querySelector(target) : target;
        if (!element) return;
        
        const elementPosition = element.offsetTop;
        const offsetPosition = elementPosition - offset;
        
        window.scrollTo({
            top: offsetPosition,
            behavior: 'smooth'
        });
    }

    /**
     * 延迟执行
     * @param {number} ms - 延迟时间（毫秒）
     * @returns {Promise} Promise对象
     */
    static delay(ms) {
        return new Promise(resolve => setTimeout(resolve, ms));
    }

    /**
     * 重试函数
     * @param {Function} fn - 要重试的函数
     * @param {number} maxRetries - 最大重试次数
     * @param {number} delay - 重试间隔
     * @returns {Promise} Promise对象
     */
    static async retry(fn, maxRetries = 3, delay = 1000) {
        let lastError;
        
        for (let i = 0; i <= maxRetries; i++) {
            try {
                return await fn();
            } catch (error) {
                lastError = error;
                if (i < maxRetries) {
                    await this.delay(delay);
                }
            }
        }
        
        throw lastError;
    }
}
