/**
 * Mika 寄售商城 - 主样式文件
 * 极简风 + iOS风格设计
 * 蓝色系专业感 + 橙色系活力感
 */

/* CSS变量定义 */
:root {
    /* 主色调 - 蓝色系专业感 */
    --primary-color: #007AFF;
    --primary-light: #5AC8FA;
    --primary-dark: #0051D5;
    --primary-gradient: linear-gradient(135deg, #007AFF 0%, #5AC8FA 100%);
    
    /* 辅助色 - 橙色系活力感 */
    --secondary-color: #FF9500;
    --secondary-light: #FFCC02;
    --secondary-dark: #FF6B00;
    --secondary-gradient: linear-gradient(135deg, #FF9500 0%, #FFCC02 100%);
    
    /* 中性色 */
    --text-primary: #1D1D1F;
    --text-secondary: #86868B;
    --text-tertiary: #C7C7CC;
    --background-primary: #FFFFFF;
    --background-secondary: #F2F2F7;
    --background-tertiary: #E5E5EA;
    
    /* 状态色 */
    --success-color: #34C759;
    --warning-color: #FF9500;
    --error-color: #FF3B30;
    --info-color: #007AFF;
    
    /* 阴影 */
    --shadow-light: 0 1px 3px rgba(0, 0, 0, 0.1);
    --shadow-medium: 0 4px 12px rgba(0, 0, 0, 0.15);
    --shadow-heavy: 0 8px 24px rgba(0, 0, 0, 0.2);
    
    /* 圆角 */
    --radius-small: 8px;
    --radius-medium: 12px;
    --radius-large: 16px;
    --radius-xl: 24px;
    
    /* 间距 */
    --spacing-xs: 4px;
    --spacing-sm: 8px;
    --spacing-md: 16px;
    --spacing-lg: 24px;
    --spacing-xl: 32px;
    --spacing-2xl: 48px;
    
    /* 字体 */
    --font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', sans-serif;
    --font-size-xs: 12px;
    --font-size-sm: 14px;
    --font-size-base: 16px;
    --font-size-lg: 18px;
    --font-size-xl: 20px;
    --font-size-2xl: 24px;
    --font-size-3xl: 32px;
    
    /* 动画 */
    --transition-fast: 0.15s ease-out;
    --transition-base: 0.3s ease-out;
    --transition-slow: 0.5s ease-out;
}

/* 基础重置 */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

html {
    font-size: 16px;
    scroll-behavior: smooth;
}

body {
    font-family: var(--font-family);
    font-size: var(--font-size-base);
    line-height: 1.6;
    color: var(--text-primary);
    background-color: var(--background-primary);
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
}

/* 容器 */
.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 var(--spacing-md);
}

/* 按钮样式 */
.btn {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    padding: 12px 24px;
    border: none;
    border-radius: var(--radius-medium);
    font-size: var(--font-size-base);
    font-weight: 600;
    text-decoration: none;
    cursor: pointer;
    transition: all var(--transition-base);
    user-select: none;
    white-space: nowrap;
    min-height: 44px;
}

.btn:disabled {
    opacity: 0.5;
    cursor: not-allowed;
}

.btn-primary {
    background: var(--primary-gradient);
    color: white;
    box-shadow: var(--shadow-light);
}

.btn-primary:hover:not(:disabled) {
    transform: translateY(-1px);
    box-shadow: var(--shadow-medium);
}

.btn-primary:active {
    transform: translateY(0);
}

.btn-secondary {
    background: var(--secondary-gradient);
    color: white;
    box-shadow: var(--shadow-light);
}

.btn-secondary:hover:not(:disabled) {
    transform: translateY(-1px);
    box-shadow: var(--shadow-medium);
}

.btn-outline {
    background: transparent;
    color: var(--primary-color);
    border: 2px solid var(--primary-color);
}

.btn-outline:hover:not(:disabled) {
    background: var(--primary-color);
    color: white;
}

.btn-ghost {
    background: transparent;
    color: var(--text-secondary);
    border: 1px solid var(--background-tertiary);
}

.btn-ghost:hover:not(:disabled) {
    background: var(--background-secondary);
    color: var(--text-primary);
}

/* 输入框样式 */
.input, .select, .textarea {
    width: 100%;
    padding: 12px 16px;
    border: 2px solid var(--background-tertiary);
    border-radius: var(--radius-medium);
    font-size: var(--font-size-base);
    font-family: var(--font-family);
    background: var(--background-primary);
    color: var(--text-primary);
    transition: all var(--transition-base);
}

.input:focus, .select:focus, .textarea:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(0, 122, 255, 0.1);
}

.input::placeholder {
    color: var(--text-tertiary);
}

/* 卡片样式 */
.card {
    background: var(--background-primary);
    border-radius: var(--radius-large);
    box-shadow: var(--shadow-light);
    overflow: hidden;
    transition: all var(--transition-base);
}

.card:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-medium);
}

.card-header {
    padding: var(--spacing-lg);
    border-bottom: 1px solid var(--background-secondary);
}

.card-body {
    padding: var(--spacing-lg);
}

.card-footer {
    padding: var(--spacing-lg);
    border-top: 1px solid var(--background-secondary);
    background: var(--background-secondary);
}

/* 网格布局 */
.grid {
    display: grid;
    gap: var(--spacing-lg);
}

.grid-2 {
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
}

.grid-3 {
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
}

.grid-4 {
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
}

/* 加载动画 */
.loading-screen {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: var(--background-primary);
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    z-index: 9999;
    transition: opacity var(--transition-base);
}

.loading-screen.hidden {
    opacity: 0;
    pointer-events: none;
}

.loading-spinner {
    width: 40px;
    height: 40px;
    border: 3px solid var(--background-tertiary);
    border-top: 3px solid var(--primary-color);
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin-bottom: var(--spacing-md);
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* 工具类 */
.text-center { text-align: center; }
.text-left { text-align: left; }
.text-right { text-align: right; }

.hidden { display: none !important; }
.visible { display: block !important; }

.mt-0 { margin-top: 0; }
.mt-1 { margin-top: var(--spacing-xs); }
.mt-2 { margin-top: var(--spacing-sm); }
.mt-3 { margin-top: var(--spacing-md); }
.mt-4 { margin-top: var(--spacing-lg); }
.mt-5 { margin-top: var(--spacing-xl); }

.mb-0 { margin-bottom: 0; }
.mb-1 { margin-bottom: var(--spacing-xs); }
.mb-2 { margin-bottom: var(--spacing-sm); }
.mb-3 { margin-bottom: var(--spacing-md); }
.mb-4 { margin-bottom: var(--spacing-lg); }
.mb-5 { margin-bottom: var(--spacing-xl); }

/* Header样式 */
.header {
    position: sticky;
    top: 0;
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(20px);
    -webkit-backdrop-filter: blur(20px);
    border-bottom: 1px solid var(--background-secondary);
    z-index: 1000;
}

.header-content {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: var(--spacing-md) 0;
}

.logo h1 {
    font-size: var(--font-size-xl);
    font-weight: 700;
    color: var(--text-primary);
}

.nav {
    display: flex;
    gap: var(--spacing-lg);
}

.nav-link {
    color: var(--text-secondary);
    text-decoration: none;
    font-weight: 500;
    padding: var(--spacing-sm) var(--spacing-md);
    border-radius: var(--radius-small);
    transition: all var(--transition-base);
}

.nav-link:hover,
.nav-link.active {
    color: var(--primary-color);
    background: rgba(0, 122, 255, 0.1);
}

.header-actions {
    display: flex;
    align-items: center;
    gap: var(--spacing-md);
}

.btn-search {
    background: none;
    border: none;
    padding: var(--spacing-sm);
    border-radius: var(--radius-small);
    cursor: pointer;
    color: var(--text-secondary);
    transition: all var(--transition-base);
}

.btn-search:hover {
    background: var(--background-secondary);
    color: var(--primary-color);
}

/* 搜索栏样式 */
.search-container {
    background: var(--background-primary);
    border-bottom: 1px solid var(--background-secondary);
    transform: translateY(-100%);
    transition: transform var(--transition-base);
    position: sticky;
    top: 73px;
    z-index: 999;
}

.search-container.active {
    transform: translateY(0);
}

.search-bar {
    display: flex;
    align-items: center;
    gap: var(--spacing-md);
    padding: var(--spacing-md) 0;
}

.search-bar input {
    flex: 1;
    border: none;
    background: var(--background-secondary);
    padding: 12px 16px;
    border-radius: var(--radius-medium);
    font-size: var(--font-size-base);
}

.search-submit {
    background: var(--primary-gradient);
    color: white;
    border: none;
    padding: 12px 24px;
    border-radius: var(--radius-medium);
    font-weight: 600;
    cursor: pointer;
    transition: all var(--transition-base);
}

.search-close {
    background: none;
    border: none;
    font-size: var(--font-size-lg);
    color: var(--text-secondary);
    cursor: pointer;
    padding: var(--spacing-sm);
    border-radius: var(--radius-small);
    transition: all var(--transition-base);
}

.search-close:hover {
    background: var(--background-secondary);
    color: var(--text-primary);
}

/* 主内容区域 */
.main {
    min-height: calc(100vh - 140px);
}

.page {
    display: none;
    animation: fadeIn var(--transition-base);
}

.page.active {
    display: block;
}

@keyframes fadeIn {
    from { opacity: 0; transform: translateY(20px); }
    to { opacity: 1; transform: translateY(0); }
}

/* Hero区域 */
.hero {
    background: var(--primary-gradient);
    color: white;
    padding: var(--spacing-2xl) 0;
    text-align: center;
}

.hero-content h2 {
    font-size: var(--font-size-3xl);
    font-weight: 700;
    margin-bottom: var(--spacing-md);
}

.hero-content p {
    font-size: var(--font-size-lg);
    opacity: 0.9;
    margin-bottom: var(--spacing-xl);
}

.hero-actions {
    display: flex;
    gap: var(--spacing-md);
    justify-content: center;
    flex-wrap: wrap;
}

.hero-actions .btn {
    min-width: 140px;
}

.hero-actions .btn-secondary {
    background: rgba(255, 255, 255, 0.2);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.3);
}

.hero-actions .btn-secondary:hover {
    background: rgba(255, 255, 255, 0.3);
}

/* 特色商店区域 */
.featured-shops {
    padding: var(--spacing-2xl) 0;
    background: var(--background-secondary);
}

.featured-shops h3 {
    font-size: var(--font-size-2xl);
    font-weight: 700;
    text-align: center;
    margin-bottom: var(--spacing-xl);
    color: var(--text-primary);
}

/* 商店网格 */
.shops-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
    gap: var(--spacing-lg);
    margin-top: var(--spacing-lg);
}

.shop-card {
    background: var(--background-primary);
    border-radius: var(--radius-large);
    padding: var(--spacing-lg);
    box-shadow: var(--shadow-light);
    transition: all var(--transition-base);
    cursor: pointer;
    border: 2px solid transparent;
}

.shop-card:hover {
    transform: translateY(-4px);
    box-shadow: var(--shadow-medium);
    border-color: var(--primary-color);
}

.shop-card-header {
    display: flex;
    align-items: center;
    gap: var(--spacing-md);
    margin-bottom: var(--spacing-md);
}

.shop-avatar {
    width: 48px;
    height: 48px;
    background: var(--primary-gradient);
    border-radius: var(--radius-medium);
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: var(--font-size-xl);
    color: white;
    font-weight: 700;
}

.shop-info h4 {
    font-size: var(--font-size-lg);
    font-weight: 600;
    color: var(--text-primary);
    margin-bottom: var(--spacing-xs);
}

.shop-info .shop-id {
    font-size: var(--font-size-sm);
    color: var(--text-secondary);
}

.shop-description {
    color: var(--text-secondary);
    margin-bottom: var(--spacing-md);
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
}

.shop-stats {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding-top: var(--spacing-md);
    border-top: 1px solid var(--background-secondary);
}

.shop-stat {
    text-align: center;
}

.shop-stat-value {
    font-size: var(--font-size-lg);
    font-weight: 600;
    color: var(--primary-color);
}

.shop-stat-label {
    font-size: var(--font-size-sm);
    color: var(--text-secondary);
}

/* 页面头部 */
.page-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: var(--spacing-xl);
    padding: var(--spacing-xl) 0 var(--spacing-lg) 0;
}

.page-header h2 {
    font-size: var(--font-size-2xl);
    font-weight: 700;
    color: var(--text-primary);
}

.filters {
    display: flex;
    gap: var(--spacing-md);
    align-items: center;
}

/* 商品网格 */
.products-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
    gap: var(--spacing-lg);
    margin-top: var(--spacing-lg);
}

.product-card {
    background: var(--background-primary);
    border-radius: var(--radius-large);
    overflow: hidden;
    box-shadow: var(--shadow-light);
    transition: all var(--transition-base);
    cursor: pointer;
    border: 2px solid transparent;
}

.product-card:hover {
    transform: translateY(-4px);
    box-shadow: var(--shadow-medium);
    border-color: var(--primary-color);
}

.product-image {
    width: 100%;
    height: 200px;
    background: var(--secondary-gradient);
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: var(--font-size-3xl);
    color: white;
}

.product-content {
    padding: var(--spacing-lg);
}

.product-title {
    font-size: var(--font-size-lg);
    font-weight: 600;
    color: var(--text-primary);
    margin-bottom: var(--spacing-sm);
    display: -webkit-box;
    -webkit-line-clamp: 1;
    -webkit-box-orient: vertical;
    overflow: hidden;
}

.product-description {
    color: var(--text-secondary);
    margin-bottom: var(--spacing-md);
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
}

.product-footer {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding-top: var(--spacing-md);
    border-top: 1px solid var(--background-secondary);
}

.product-price {
    font-size: var(--font-size-xl);
    font-weight: 700;
    color: var(--primary-color);
}

.product-stock {
    font-size: var(--font-size-sm);
    color: var(--text-secondary);
}

.product-stock.out-of-stock {
    color: var(--error-color);
    font-weight: 600;
}

.product-stock.low-stock {
    color: var(--warning-color);
    font-weight: 600;
}

/* 商店详情页 */
.shop-header {
    background: var(--primary-gradient);
    color: white;
    padding: var(--spacing-2xl) 0;
    margin-bottom: var(--spacing-xl);
    border-radius: var(--radius-large);
}

.shop-detail {
    display: flex;
    align-items: center;
    gap: var(--spacing-xl);
}

.shop-detail-avatar {
    width: 80px;
    height: 80px;
    background: rgba(255, 255, 255, 0.2);
    border-radius: var(--radius-large);
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: var(--font-size-3xl);
    font-weight: 700;
    backdrop-filter: blur(10px);
}

.shop-detail-info h2 {
    font-size: var(--font-size-3xl);
    font-weight: 700;
    margin-bottom: var(--spacing-sm);
}

.shop-detail-description {
    font-size: var(--font-size-lg);
    opacity: 0.9;
    margin-bottom: var(--spacing-md);
}

.shop-detail-actions {
    display: flex;
    gap: var(--spacing-md);
    flex-wrap: wrap;
}

.shop-detail-actions .btn {
    background: rgba(255, 255, 255, 0.2);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.3);
    color: white;
}

.shop-detail-actions .btn:hover {
    background: rgba(255, 255, 255, 0.3);
}

/* 商品详情页 */
.product-detail {
    max-width: 800px;
    margin: 0 auto;
    padding: var(--spacing-xl) 0;
}

.product-detail-header {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: var(--spacing-2xl);
    margin-bottom: var(--spacing-2xl);
}

.product-detail-image {
    width: 100%;
    height: 400px;
    background: var(--secondary-gradient);
    border-radius: var(--radius-large);
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 4rem;
    color: white;
}

.product-detail-info h1 {
    font-size: var(--font-size-3xl);
    font-weight: 700;
    color: var(--text-primary);
    margin-bottom: var(--spacing-md);
}

.product-detail-price {
    font-size: var(--font-size-2xl);
    font-weight: 700;
    color: var(--primary-color);
    margin-bottom: var(--spacing-md);
}

.product-detail-stock {
    font-size: var(--font-size-base);
    color: var(--text-secondary);
    margin-bottom: var(--spacing-lg);
}

.product-detail-description {
    background: var(--background-secondary);
    padding: var(--spacing-lg);
    border-radius: var(--radius-medium);
    margin-bottom: var(--spacing-xl);
}

.product-detail-description h3 {
    font-size: var(--font-size-lg);
    font-weight: 600;
    color: var(--text-primary);
    margin-bottom: var(--spacing-md);
}

.product-detail-actions {
    display: flex;
    gap: var(--spacing-md);
    flex-wrap: wrap;
}

.product-detail-actions .btn {
    flex: 1;
    min-width: 140px;
}

/* 订单页面 */
.order-detail {
    max-width: 600px;
    margin: 0 auto;
    padding: var(--spacing-xl) 0;
}

.order-card {
    background: var(--background-primary);
    border-radius: var(--radius-large);
    padding: var(--spacing-xl);
    box-shadow: var(--shadow-medium);
    margin-bottom: var(--spacing-lg);
}

.order-header {
    text-align: center;
    margin-bottom: var(--spacing-xl);
}

.order-header h2 {
    font-size: var(--font-size-2xl);
    font-weight: 700;
    color: var(--text-primary);
    margin-bottom: var(--spacing-sm);
}

.order-id {
    font-size: var(--font-size-sm);
    color: var(--text-secondary);
    font-family: monospace;
}

.order-info {
    margin-bottom: var(--spacing-xl);
}

.order-info-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: var(--spacing-md) 0;
    border-bottom: 1px solid var(--background-secondary);
}

.order-info-item:last-child {
    border-bottom: none;
}

.order-info-label {
    color: var(--text-secondary);
    font-weight: 500;
}

.order-info-value {
    color: var(--text-primary);
    font-weight: 600;
}

.order-total {
    font-size: var(--font-size-xl);
    color: var(--primary-color);
}

.order-actions {
    display: flex;
    gap: var(--spacing-md);
    flex-wrap: wrap;
}

.order-actions .btn {
    flex: 1;
    min-width: 140px;
}

/* 支付方式选择 */
.payment-methods {
    margin-bottom: var(--spacing-lg);
}

.payment-methods h3 {
    font-size: var(--font-size-lg);
    font-weight: 600;
    color: var(--text-primary);
    margin-bottom: var(--spacing-md);
}

.payment-options {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
    gap: var(--spacing-md);
}

.payment-option {
    background: var(--background-primary);
    border: 2px solid var(--background-tertiary);
    border-radius: var(--radius-medium);
    padding: var(--spacing-lg);
    text-align: center;
    cursor: pointer;
    transition: all var(--transition-base);
}

.payment-option:hover {
    border-color: var(--primary-color);
    background: rgba(0, 122, 255, 0.05);
}

.payment-option.selected {
    border-color: var(--primary-color);
    background: rgba(0, 122, 255, 0.1);
}

.payment-option-icon {
    font-size: var(--font-size-2xl);
    margin-bottom: var(--spacing-sm);
}

.payment-option-name {
    font-weight: 600;
    color: var(--text-primary);
}

/* 模态框 */
.modal {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.5);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 10000;
    opacity: 0;
    visibility: hidden;
    transition: all var(--transition-base);
}

.modal.active {
    opacity: 1;
    visibility: visible;
}

.modal-content {
    background: var(--background-primary);
    border-radius: var(--radius-large);
    max-width: 500px;
    width: 90%;
    max-height: 80vh;
    overflow-y: auto;
    box-shadow: var(--shadow-heavy);
    transform: scale(0.9);
    transition: transform var(--transition-base);
}

.modal.active .modal-content {
    transform: scale(1);
}

.modal-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: var(--spacing-lg);
    border-bottom: 1px solid var(--background-secondary);
}

.modal-header h3 {
    font-size: var(--font-size-lg);
    font-weight: 600;
    color: var(--text-primary);
}

.modal-close {
    background: none;
    border: none;
    font-size: var(--font-size-lg);
    color: var(--text-secondary);
    cursor: pointer;
    padding: var(--spacing-sm);
    border-radius: var(--radius-small);
    transition: all var(--transition-base);
}

.modal-close:hover {
    background: var(--background-secondary);
    color: var(--text-primary);
}

.modal-body {
    padding: var(--spacing-lg);
}

.modal-footer {
    padding: var(--spacing-lg);
    border-top: 1px solid var(--background-secondary);
    display: flex;
    gap: var(--spacing-md);
    justify-content: flex-end;
}

/* Toast通知 */
.toast-container {
    position: fixed;
    top: 20px;
    right: 20px;
    z-index: 10001;
    display: flex;
    flex-direction: column;
    gap: var(--spacing-sm);
}

.toast {
    background: var(--background-primary);
    border-radius: var(--radius-medium);
    padding: var(--spacing-md) var(--spacing-lg);
    box-shadow: var(--shadow-medium);
    border-left: 4px solid var(--primary-color);
    min-width: 300px;
    transform: translateX(100%);
    transition: transform var(--transition-base);
}

.toast.show {
    transform: translateX(0);
}

.toast.success {
    border-left-color: var(--success-color);
}

.toast.error {
    border-left-color: var(--error-color);
}

.toast.warning {
    border-left-color: var(--warning-color);
}

.toast.info {
    border-left-color: var(--info-color);
}

.toast-content {
    display: flex;
    align-items: center;
    gap: var(--spacing-md);
}

.toast-icon {
    font-size: var(--font-size-lg);
}

.toast-message {
    flex: 1;
    color: var(--text-primary);
    font-weight: 500;
}

/* Footer */
.footer {
    background: var(--background-secondary);
    padding: var(--spacing-xl) 0;
    margin-top: var(--spacing-2xl);
}

.footer-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
    flex-wrap: wrap;
    gap: var(--spacing-md);
}

.footer-info {
    color: var(--text-secondary);
}

.footer-links {
    display: flex;
    gap: var(--spacing-lg);
}

.footer-links a {
    color: var(--text-secondary);
    text-decoration: none;
    transition: color var(--transition-base);
}

.footer-links a:hover {
    color: var(--primary-color);
}
