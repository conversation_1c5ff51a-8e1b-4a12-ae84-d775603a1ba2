#!/usr/bin/env python3
"""
测试API错误处理的脚本
用于验证JSON解析错误的处理是否正确
"""

import requests
import json
import logging

# 配置日志
logging.basicConfig(
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    level=logging.INFO
)
logger = logging.getLogger(__name__)

async def safe_api_call(url, api_name="API", timeout=30):
    """
    安全的API调用函数，包含详细的错误处理和日志记录
    
    Args:
        url (str): API请求URL
        api_name (str): API名称，用于日志记录
        timeout (int): 请求超时时间
        
    Returns:
        tuple: (success: bool, data: dict or None, error_msg: str or None)
    """
    try:
        logger.info(f"{api_name}请求URL: {url}")
        response = requests.get(url, timeout=timeout)
        
        if response.status_code == 200:
            # 记录响应内容用于调试
            response_text = response.text
            logger.info(f"{api_name}响应内容: {response_text[:500]}...")
            
            try:
                data = response.json()
                return True, data, None
            except json.JSONDecodeError as e:
                error_msg = f"{api_name}JSON解析失败: {str(e)}"
                logger.error(error_msg)
                logger.error(f"响应内容: {response_text}")
                return False, None, "服务器返回格式错误，请稍后重试"
        else:
            error_msg = f"{api_name}请求失败，状态码: {response.status_code}"
            logger.error(error_msg)
            return False, None, "服务器错误，请稍后重试"
            
    except requests.exceptions.Timeout:
        error_msg = f"{api_name}请求超时"
        logger.error(error_msg)
        return False, None, "请求超时，请稍后重试"
    except requests.exceptions.RequestException as e:
        error_msg = f"{api_name}请求异常: {str(e)}"
        logger.error(error_msg)
        return False, None, "查询失败，请稍后重试"
    except Exception as e:
        error_msg = f"{api_name}其他异常: {str(e)}"
        logger.error(error_msg)
        return False, None, "查询失败，请稍后重试"

def test_api_error_scenarios():
    """测试各种API错误场景"""
    
    print("=== API错误处理测试 ===\n")
    
    # 测试场景1: 无效的URL（会导致连接错误）
    print("1. 测试无效URL...")
    success, data, error_msg = safe_api_call("http://invalid-url-that-does-not-exist.com/api", "无效URL测试")
    print(f"结果: success={success}, error_msg={error_msg}\n")
    
    # 测试场景2: 返回非JSON内容的URL
    print("2. 测试返回非JSON内容...")
    success, data, error_msg = safe_api_call("https://www.google.com", "非JSON测试")
    print(f"结果: success={success}, error_msg={error_msg}\n")
    
    # 测试场景3: 404错误
    print("3. 测试404错误...")
    success, data, error_msg = safe_api_call("https://httpbin.org/status/404", "404测试")
    print(f"结果: success={success}, error_msg={error_msg}\n")
    
    # 测试场景4: 500错误
    print("4. 测试500错误...")
    success, data, error_msg = safe_api_call("https://httpbin.org/status/500", "500测试")
    print(f"结果: success={success}, error_msg={error_msg}\n")
    
    # 测试场景5: 正常的JSON响应
    print("5. 测试正常JSON响应...")
    success, data, error_msg = safe_api_call("https://httpbin.org/json", "正常JSON测试")
    print(f"结果: success={success}, data={data}, error_msg={error_msg}\n")

def analyze_original_error():
    """分析原始错误的可能原因"""
    
    print("=== 原始错误分析 ===\n")
    
    print("错误信息: 'Expecting value: line 1 column 1 (char 0)'")
    print("这个错误表示:")
    print("1. 服务器返回了空响应")
    print("2. 服务器返回了非JSON格式的内容")
    print("3. 响应被截断或损坏")
    print("4. 服务器虽然返回200状态码，但实际内容有问题\n")
    
    print("建议的解决方案:")
    print("1. 增加响应内容日志记录")
    print("2. 添加JSON解析异常处理")
    print("3. 检查API服务器状态")
    print("4. 添加重试机制")
    print("5. 验证请求参数格式\n")

if __name__ == "__main__":
    analyze_original_error()
    
    # 注意：这里使用同步版本进行测试
    # 在实际bot中使用的是async版本
    import asyncio
    
    # 创建同步版本用于测试
    def safe_api_call_sync(url, api_name="API", timeout=30):
        try:
            logger.info(f"{api_name}请求URL: {url}")
            response = requests.get(url, timeout=timeout)
            
            if response.status_code == 200:
                response_text = response.text
                logger.info(f"{api_name}响应内容: {response_text[:500]}...")
                
                try:
                    data = response.json()
                    return True, data, None
                except json.JSONDecodeError as e:
                    error_msg = f"{api_name}JSON解析失败: {str(e)}"
                    logger.error(error_msg)
                    logger.error(f"响应内容: {response_text}")
                    return False, None, "服务器返回格式错误，请稍后重试"
            else:
                error_msg = f"{api_name}请求失败，状态码: {response.status_code}"
                logger.error(error_msg)
                return False, None, "服务器错误，请稍后重试"
                
        except requests.exceptions.Timeout:
            error_msg = f"{api_name}请求超时"
            logger.error(error_msg)
            return False, None, "请求超时，请稍后重试"
        except requests.exceptions.RequestException as e:
            error_msg = f"{api_name}请求异常: {str(e)}"
            logger.error(error_msg)
            return False, None, "查询失败，请稍后重试"
        except Exception as e:
            error_msg = f"{api_name}其他异常: {str(e)}"
            logger.error(error_msg)
            return False, None, "查询失败，请稍后重试"
    
    # 重新定义全局函数用于测试
    safe_api_call = safe_api_call_sync
    test_api_error_scenarios()
