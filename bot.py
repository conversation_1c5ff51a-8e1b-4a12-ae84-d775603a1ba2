import logging
import datetime
import requests
import time
import ssl
import certifi
import asyncio
import random
import os
import tempfile
import json
import re
from telegram import Update, InlineKeyboardButton, InlineKeyboardMarkup
from telegram.ext import Application, CommandHandler, MessageHandler, CallbackQueryHandler, filters, ContextTypes
from telegram.error import TimedOut, NetworkError
from config import *
from database import Database
from functools import wraps

# 配置日志
logging.basicConfig(
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    level=logging.INFO
)
logger = logging.getLogger(__name__)

# 初始化数据库
db = Database()

# 检测WAF验证页面
def is_waf_challenge(response_text):
    """检测是否为WAF验证页面"""
    waf_indicators = [
        "Verify Yourself",
        "GOEDGE_WAF_CAPTCHA_ID",
        "Slide to Unlock",
        "Request ID:",
        "WAF_CAPTCHA"
    ]
    return any(indicator in response_text for indicator in waf_indicators)

# 通用API调用函数
async def safe_api_call(url, api_name="API", timeout=30, headers=None):
    """
    安全的API调用函数，包含详细的错误处理和日志记录

    Args:
        url (str): API请求URL
        api_name (str): API名称，用于日志记录
        timeout (int): 请求超时时间
        headers (dict): 请求头

    Returns:
        tuple: (success: bool, data: dict or None, error_msg: str or None)
    """
    try:
        # 设置默认请求头，模拟浏览器访问
        default_headers = {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
            'Accept': 'application/json, text/plain, */*',
            'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
            'Accept-Encoding': 'gzip, deflate, br',
            'Connection': 'keep-alive',
            'Referer': 'https://api.qnm6.top/',
        }

        if headers:
            default_headers.update(headers)

        logger.info(f"{api_name}请求URL: {url}")
        response = requests.get(url, timeout=timeout, headers=default_headers)

        if response.status_code == 200:
            # 记录响应内容用于调试
            response_text = response.text
            logger.info(f"{api_name}响应内容: {response_text[:500]}...")

            # 检测WAF验证页面
            if is_waf_challenge(response_text):
                logger.warning(f"{api_name}遇到WAF验证页面")
                return False, None, "API服务器需要人机验证，请稍后重试"

            try:
                data = response.json()
                return True, data, None
            except json.JSONDecodeError as e:
                error_msg = f"{api_name}JSON解析失败: {str(e)}"
                logger.error(error_msg)
                logger.error(f"响应内容: {response_text}")
                return False, None, "服务器返回格式错误，请稍后重试"
        else:
            error_msg = f"{api_name}请求失败，状态码: {response.status_code}"
            logger.error(error_msg)
            return False, None, "服务器错误，请稍后重试"

    except requests.exceptions.Timeout:
        error_msg = f"{api_name}请求超时"
        logger.error(error_msg)
        return False, None, "请求超时，请稍后重试"
    except requests.exceptions.RequestException as e:
        error_msg = f"{api_name}请求异常: {str(e)}"
        logger.error(error_msg)
        return False, None, "查询失败，请稍后重试"
    except Exception as e:
        error_msg = f"{api_name}其他异常: {str(e)}"
        logger.error(error_msg)
        return False, None, "查询失败，请稍后重试"

# 带重试机制的API调用函数
async def safe_api_call_with_retry(url, api_name="API", timeout=30, max_retries=3, delay_between_retries=2):
    """
    带重试机制的API调用函数，专门处理WAF验证问题

    Args:
        url (str): API请求URL
        api_name (str): API名称，用于日志记录
        timeout (int): 请求超时时间
        max_retries (int): 最大重试次数
        delay_between_retries (int): 重试间隔时间（秒）

    Returns:
        tuple: (success: bool, data: dict or None, error_msg: str or None)
    """
    last_error_msg = None

    for attempt in range(max_retries):
        logger.info(f"{api_name}第{attempt + 1}次尝试...")

        success, data, error_msg = await safe_api_call(url, f"{api_name}(尝试{attempt + 1})", timeout)

        if success:
            if attempt > 0:
                logger.info(f"{api_name}在第{attempt + 1}次尝试后成功")
            return success, data, error_msg

        last_error_msg = error_msg

        # 如果是WAF验证问题，等待更长时间
        if "人机验证" in error_msg:
            wait_time = delay_between_retries * (attempt + 1) * 2  # 指数退避
            logger.warning(f"{api_name}遇到WAF验证，等待{wait_time}秒后重试...")
        else:
            wait_time = delay_between_retries * (attempt + 1)
            logger.warning(f"{api_name}第{attempt + 1}次尝试失败: {error_msg}")

        if attempt < max_retries - 1:
            logger.info(f"等待{wait_time}秒后进行第{attempt + 2}次尝试...")
            await asyncio.sleep(wait_time)

    logger.error(f"{api_name}重试{max_retries}次后仍然失败")
    return False, None, f"服务器繁忙，重试{max_retries}次后仍然失败，请稍后再试"

# API配置
API_TOKEN = "0edf486d44ad7f0bba77a49f947f8cd4"
API_BASE_URL = "https://api.qnm6.top/api"


def check_group_and_channel(func):
    @wraps(func)
    async def wrapper(update: Update, context: ContextTypes.DEFAULT_TYPE, *args, **kwargs):
        user_id = update.effective_user.id
        bot = context.bot
        # 检查频道
        try:
            channel_member = await bot.get_chat_member('@idatas8', user_id)
            if channel_member.status not in ['member', 'administrator', 'creator']:
                await update.message.reply_text(
                    "请先加入我们的频道后再使用本功能：\n频道：https://t.me/idatas8\n群组：https://t.me/idatas6",
                    parse_mode='HTML'
                )
                return
        except Exception:
            await update.message.reply_text(
                "请先加入我们的频道后再使用本功能：\n频道：https://t.me/idatas8\n群组：https://t.me/idatas6",
                parse_mode='HTML'
            )
            return
        # 检查群组
        try:
            group_member = await bot.get_chat_member('@idatas6', user_id)
            if group_member.status not in ['member', 'administrator', 'creator']:
                await update.message.reply_text(
                    "请先加入我们的群组后再使用本功能：\n频道：https://t.me/idatas8\n群组：https://t.me/idatas6",
                    parse_mode='HTML'
                )
                return
        except Exception:
            await update.message.reply_text(
                "请先加入我们的群组后再使用本功能：\n频道：https://t.me/idatas8\n群组：https://t.me/idatas6",
                parse_mode='HTML'
            )
            return
        return await func(update, context, *args, **kwargs)
    return wrapper


async def create_session():
    # 创建自定义SSL上下文
    ssl_context = ssl.create_default_context(cafile=certifi.where())
    
    # 配置连接器
    connector = aiohttp.TCPConnector(
        ssl=ssl_context,
        force_close=True,
        enable_cleanup_closed=True
    )
    
    return aiohttp.ClientSession(connector=connector)

async def start(update: Update, context: ContextTypes.DEFAULT_TYPE):
    user_id = update.effective_user.id
    user = db.get_user(user_id)
    
    # 检查是否有邀请人
    if context.args and context.args[0].startswith('invite_'):
        inviter_id = int(context.args[0].split('_')[1])
        if inviter_id != user_id and not user["invited_by"]:
            db.update_user(user_id, {"invited_by": inviter_id})
            db.add_invite(inviter_id, user_id)
            db.increment_daily_invites(inviter_id)
            
            # 给邀请人奖励
            inviter = db.get_user(inviter_id)
            db.update_user(inviter_id, {"tries": inviter["tries"] + 1})
            
            # 检查是否达到每日邀请奖励条件
            daily_invites = db.get_daily_invites(inviter_id)
            total_invites = inviter["invite_count"] + 1
            
            # 如果达到3人，设置1天会员
            if daily_invites >= 3:
                vip_until = datetime.datetime.now() + datetime.timedelta(days=1)
                db.update_user(inviter_id, {
                    "is_vip": True,
                    "vip_until": vip_until.isoformat()
                })
            
            # 构建邀请成功消息
            invite_success_text = f"""
🎉 <b>邀请成功！</b>

✅ 成功邀请新用户加入
🎁 获得1次查询机会

📊 <b>邀请统计：</b>
• 今日邀请：{daily_invites} 人
• 总邀请：{total_invites} 人

💎 <b>邀请奖励：</b>"""
            
            # 根据邀请数量添加不同的激励文案
            if daily_invites >= 3:
                invite_success_text += f"\n• 恭喜获得1天会员资格！\n• 会员到期时间：{vip_until.strftime('%Y-%m-%d %H:%M:%S')}"
            elif daily_invites == 2:
                invite_success_text += "\n• 再邀请1人即可获得1天会员资格！"
            elif daily_invites == 1:
                invite_success_text += "\n• 再邀请2人即可获得1天会员资格！"
            else:
                invite_success_text += "\n• 再邀请3人即可获得1天会员资格！"
            
            # 根据总邀请数添加额外奖励提示
            if total_invites >= 20:
                invite_success_text += "\n• 恭喜获得15天会员资格！"
            elif total_invites >= 10:
                invite_success_text += "\n• 再邀请10人即可获得15天会员资格！"
            elif total_invites >= 5:
                invite_success_text += "\n• 再邀请5人即可获得5天会员资格！"
            else:
                invite_success_text += "\n• 邀请5人即可获得2天会员资格！"
            
            # 添加激励文案
            invite_success_text += f"""

💡 <b>继续邀请更多好友：</b>
• 邀请越多，奖励越丰厚！
• 邀请好友一起体验更多功能！
• 快来邀请更多好友吧！

👥 <b>邀请链接：</b>
<code>https://t.me/{context.bot.username}?start=invite_{inviter_id}</code>
"""
            
            await context.bot.send_message(
                chat_id=inviter_id,
                text=invite_success_text,
                parse_mode='HTML'
            )

    welcome_text = f"""
👋 欢迎使用 <b>iDaTas公益机器人</b>！

💡 <b>功能说明：</b>
• 签到得查询次数
• 邀请3人享1天无限查！
• 新人赠送{NEW_USER_FREE_TRIES}次体验机会

🌐 <b>高级版体验：</b>
🔗 <a href="{WEB_LINK}">点击进入</a>
高级版享受更多高级数据与功能！
"""
    
    keyboard = [
        [InlineKeyboardButton("👤 个人中心", callback_data="profile"),
         InlineKeyboardButton("📝 每日签到", callback_data="sign_in")],
        [InlineKeyboardButton("👥 邀请好友", callback_data="invite")],
        [InlineKeyboardButton("🔧 功能菜单", callback_data="features")]
    ]
    
    reply_markup = InlineKeyboardMarkup(keyboard)
    await update.message.reply_html(welcome_text, reply_markup=reply_markup)

async def button_callback(update: Update, context: ContextTypes.DEFAULT_TYPE):
    query = update.callback_query
    user_id = query.from_user.id
    user = db.get_user(user_id)
    
    await query.answer()
    
    # 处理广播按钮回调
    if query.data.startswith("broadcast_"):
        if user_id not in ADMIN_IDS:
            await query.edit_message_text("❌ 您没有权限执行此操作")
            return
            
        broadcast_id = query.data.replace("broadcast_", "", 1)
        content = db.get_broadcast(broadcast_id)
        if not content:
            await query.answer("广播消息不存在")
            return
            
        users = db.get_all_users()  # 获取所有用户ID
        total_users = len(users)
        
        # 更新消息为处理中状态
        await query.edit_message_text(
            f"""
📢 <b>广播进行中</b>

内容：
{content}

总用户数：{total_users}
已发送：0
成功：0
失败：0
进度：0%

⏳ 正在发送中...
""",
            parse_mode='HTML'
        )
        
        # 分批发送
        batch_size = 30  # 每批发送30条
        delay = 1  # 每批之间延迟1秒
        success_count = 0
        fail_count = 0
        
        for i in range(0, total_users, batch_size):
            batch = users[i:i + batch_size]
            tasks = []
            
            for user_id in batch:
                task = send_broadcast(context.bot, content, user_id, user_id, query.message.message_id)
                tasks.append(task)
            
            # 等待当前批次完成
            results = await asyncio.gather(*tasks)
            success_count += sum(1 for r in results if r)
            fail_count += sum(1 for r in results if not r)
            
            # 更新进度
            progress = (i + len(batch)) / total_users * 100
            await query.edit_message_text(
                f"""
📢 <b>广播进行中</b>

内容：
{content}

总用户数：{total_users}
已发送：{i + len(batch)}
成功：{success_count}
失败：{fail_count}
进度：{progress:.1f}%

⏳ 正在发送中...
""",
                parse_mode='HTML'
            )
            
            # 批次间延迟
            await asyncio.sleep(delay)
        
        # 发送完成，更新最终状态
        final_text = f"""
✅ <b>广播完成</b>

内容：
{content}

📊 <b>发送统计：</b>
• 总用户数：{total_users}
• 成功：{success_count}
• 失败：{fail_count}
• 成功率：{(success_count/total_users*100):.1f}%

⏱ 发送时间：{datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')}
"""
        
        # 添加重试失败用户的按钮
        if fail_count > 0:
            keyboard = [[InlineKeyboardButton("🔄 重试失败用户", callback_data=f"retry_broadcast_{broadcast_id}")]]
            reply_markup = InlineKeyboardMarkup(keyboard)
            await query.edit_message_text(final_text, parse_mode='HTML', reply_markup=reply_markup)
        else:
            await query.edit_message_text(final_text, parse_mode='HTML')
        return

    # 处理重试广播按钮回调
    if query.data.startswith("retry_broadcast_"):
        if user_id not in ADMIN_IDS:
            await query.edit_message_text("❌ 您没有权限执行此操作")
            return
            
        broadcast_id = query.data.replace("retry_broadcast_", "", 1)
        content = db.get_broadcast(broadcast_id)
        if not content:
            await query.answer("广播消息不存在")
            return
        
        # 获取上次广播的失败用户
        failed_users = db.get_failed_broadcast_users()
        if not failed_users:
            await query.answer("没有需要重试的用户")
            return
        
        # 更新消息为处理中状态
        await query.edit_message_text(
            f"""
📢 <b>重试广播</b>

内容：
{content}

失败用户数：{len(failed_users)}
已发送：0
成功：0
失败：0
进度：0%

⏳ 正在发送中...
""",
            parse_mode='HTML'
        )
        
        # 分批发送
        batch_size = 30
        delay = 1
        success_count = 0
        fail_count = 0
        
        for i in range(0, len(failed_users), batch_size):
            batch = failed_users[i:i + batch_size]
            tasks = []
            
            for user_id in batch:
                task = send_broadcast(context.bot, content, user_id, user_id, query.message.message_id)
                tasks.append(task)
            
            results = await asyncio.gather(*tasks)
            success_count += sum(1 for r in results if r)
            fail_count += sum(1 for r in results if not r)
            
            progress = (i + len(batch)) / len(failed_users) * 100
            await query.edit_message_text(
                f"""
📢 <b>重试广播</b>

内容：
{content}

失败用户数：{len(failed_users)}
已发送：{i + len(batch)}
成功：{success_count}
失败：{fail_count}
进度：{progress:.1f}%

⏳ 正在发送中...
""",
                parse_mode='HTML'
            )
            
            await asyncio.sleep(delay)
        
        # 更新最终状态
        final_text = f"""
✅ <b>重试完成</b>

内容：
{content}

📊 <b>发送统计：</b>
• 失败用户数：{len(failed_users)}
• 成功：{success_count}
• 失败：{fail_count}
• 成功率：{(success_count/len(failed_users)*100):.1f}%

⏱ 发送时间：{datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')}
"""
        
        await query.edit_message_text(final_text, parse_mode='HTML')
        return

    if query.data == "features":
        text = """
🔧 <b>功能菜单</b>

请选择需要使用的功能：

1️⃣ 假地址个户1
2️⃣ 假地址个户2
3️⃣ QQ绑定查询
4️⃣ 卡泡聆听
5️⃣ 网红猎魔
6️⃣ 二要素核验
7️⃣ 身份证正反面（新）
8️⃣ 手机号实时定位（新）

💡 使用说明：
• 点击对应功能按钮使用
• 会员用户无限次使用
• 普通用户每次使用消耗1次机会
"""
        keyboard = [
            [InlineKeyboardButton("1️⃣ 假地址个户1", callback_data="gh1"),
             InlineKeyboardButton("2️⃣ 假地址个户2", callback_data="gh2")],
            [InlineKeyboardButton("3️⃣ QQ绑定查询", callback_data="qq"),
             InlineKeyboardButton("4️⃣ 卡泡聆听", callback_data="kp")],
            [InlineKeyboardButton("5️⃣ 网红猎魔", callback_data="wh"),
             InlineKeyboardButton("6️⃣ 二要素核验", callback_data="eys")],
            [InlineKeyboardButton("7️⃣ 身份证正反面", callback_data="zfm")],
            [InlineKeyboardButton("8️⃣ 手机号实时定位", callback_data="dw")],
            [InlineKeyboardButton("🔙 返回", callback_data="back_to_start")]
        ]
        reply_markup = InlineKeyboardMarkup(keyboard)
        await query.edit_message_text(text, parse_mode='HTML', reply_markup=reply_markup)
        return

    if query.data.startswith("gh"):
        if not db.is_vip(user_id) and user["tries"] <= 0:
            keyboard = [
                [InlineKeyboardButton("📝 每日签到", callback_data="sign_in"),
                 InlineKeyboardButton("👥 邀请好友", callback_data="invite")],
                [InlineKeyboardButton("💎 开通高级版", url=WEB_LINK)]
            ]
            reply_markup = InlineKeyboardMarkup(keyboard)
            
            await query.edit_message_text(
                f"""
❌ <b>查询次数已用完</b>

💡 <b>获取更多次数：</b>
• 每日签到获得1次
• 邀请好友获得1次
• 开通会员无限次使用

✨ <b>高级版特权：</b>
• 无限次使用所有功能
• 更多高级数据查询
• 更快的查询速度
• 专属客服支持

🔥 <b>限时优惠：</b>
• 首次开通享8折优惠
• 年付会员额外赠送30天
• 邀请好友开通获得佣金

🔗 <a href="{WEB_LINK}">立即开通高级版</a>

💡 小贴士：邀请5位好友还能免费领1个月高级版会员！前6位限时领取！
🎯 点击邀请好友 → 复制链接 → 快速冲榜！

""",
                parse_mode='HTML',
                reply_markup=reply_markup
            )
            return

        # 记录功能使用次数
        if "feature_usage" not in user:
            user["feature_usage"] = 0
        user["feature_usage"] += 1
        db.update_user(user_id, {"feature_usage": user["feature_usage"]})

        # 提示用户输入信息
        text = f"""
📝 <b>请输入查询信息</b>

请使用以下格式发送：
<code>/gh{query.data[2]} 姓名 身份证号</code>

示例：
<code>/gh{query.data[2]} 张三 110101199001011234</code>

💡 提示：
• 请确保信息准确
• 姓名和身份证号之间用空格分隔
"""
        keyboard = [[InlineKeyboardButton("🔙 返回", callback_data="features")]]
        reply_markup = InlineKeyboardMarkup(keyboard)
        await query.edit_message_text(text, parse_mode='HTML', reply_markup=reply_markup)
        return

    if query.data == "qq":
        if not db.is_vip(user_id) and user["tries"] <= 0:
            keyboard = [
                [InlineKeyboardButton("📝 每日签到", callback_data="sign_in"),
                 InlineKeyboardButton("👥 邀请好友", callback_data="invite")],
                [InlineKeyboardButton("💎 开通高级版", url=WEB_LINK)]
            ]
            reply_markup = InlineKeyboardMarkup(keyboard)
            
            await query.edit_message_text(
                f"""
❌ <b>查询次数已用完</b>

💡 <b>获取更多次数：</b>
• 每日签到获得1次
• 邀请好友获得1次
• 开通会员无限次使用

✨ <b>高级版特权：</b>
• 无限次使用所有功能
• 更多高级数据查询
• 更快的查询速度
• 专属客服支持

🔥 <b>限时优惠：</b>
• 首次开通享8折优惠
• 年付会员额外赠送30天
• 邀请好友开通获得佣金

🔗 <a href="{WEB_LINK}">立即开通高级版</a>
""",
                parse_mode='HTML',
                reply_markup=reply_markup
            )
            return

        # 提示用户输入QQ号
        text = """
📝 <b>请输入QQ号</b>

请使用以下格式发送：
<code>/qb QQ号</code>

示例：
<code>/qb 10001</code>

💡 提示：
• 请确保QQ号准确
• 仅支持数字格式
"""
        keyboard = [[InlineKeyboardButton("🔙 返回", callback_data="features")]]
        reply_markup = InlineKeyboardMarkup(keyboard)
        await query.edit_message_text(text, parse_mode='HTML', reply_markup=reply_markup)
        return

    if query.data == "profile":
        # 获取用户信息
        user_info = await context.bot.get_chat(user_id)
        username = user_info.username or "未设置"
        first_name = user_info.first_name or "未设置"
        
        # 获取会员状态
        if db.is_vip(user_id):
            vip_status = "✅ 会员"
            vip_until = datetime.datetime.fromisoformat(user["vip_until"])
            vip_expiry = vip_until.strftime('%Y-%m-%d %H:%M:%S')
        else:
            vip_status = "❌ 非会员"
            vip_expiry = "未开通"
        
        # 获取查询次数
        if db.is_vip(user_id):
            tries = "∞ 无限次（会员特权）"
        else:
            tries = f"{user['tries']} 次"
        
        text = f"""
👤 <b>个人中心</b>

👤 <b>用户信息：</b>
• 用户名：@{username}
• 昵称：{first_name}
• ID：{user_id}

📊 <b>使用统计：</b>
• 查询次数：{tries}
• 总邀请：{user['invite_count']} 人
• 今日邀请：{db.get_daily_invites(user_id)} 人

👑 <b>会员状态：</b>
• 状态：{vip_status}
• 到期时间：{vip_expiry}

💡 <b>邀请奖励：</b>
• 邀请1人 = 1次查询机会
• 每日邀请3人 = 1天会员资格
• 邀请越多，奖励越多！
"""
        keyboard = [
            [InlineKeyboardButton("👥 邀请好友", callback_data="invite")],
            [InlineKeyboardButton("📝 每日签到", callback_data="sign_in"),
             InlineKeyboardButton("🔙 返回", callback_data="back_to_start")]
        ]
        reply_markup = InlineKeyboardMarkup(keyboard)
        await query.edit_message_text(text, parse_mode='HTML', reply_markup=reply_markup)
        
    elif query.data == "invite":
        bot = await context.bot.get_me()
        invite_link = f"https://t.me/{bot.username}?start=invite_{user_id}"
        text = f"""
📢 <b>邀请好友</b>

🔗 您的专属邀请链接：
<code>{invite_link}</code>

💎 <b>邀请奖励：</b>
• 邀请1人 = 1次查询机会
• 每日邀请3人 = 1天会员资格
• 邀请越多，奖励越多！

🎁 <b>额外奖励：</b>
• 邀请5人 = 2天会员
• 邀请10人 = 5天会员
• 邀请20人 = 15天会员

📊 <b>邀请统计：</b>
• 今日邀请：{db.get_daily_invites(user_id)} 人
• 总邀请：{user['invite_count']} 人

💡 <b>小贴士：</b>
邀请好友越多，获得的奖励越丰厚！
快来邀请好友一起使用吧！
"""
        keyboard = [
            [InlineKeyboardButton("👤 个人中心", callback_data="profile")],
            [InlineKeyboardButton("🔙 返回", callback_data="back_to_start")]
        ]
        reply_markup = InlineKeyboardMarkup(keyboard)
        await query.edit_message_text(text, parse_mode='HTML', reply_markup=reply_markup)
        
    elif query.data == "sign_in":
        text = f"""
❌ <b>签到失败</b>

请到群组中进行签到：
🔗 <a href="{GROUP_LINK}">点击加入群组</a>

💡 <b>签到方式：</b>
• 在群内发送 /qd
• 或直接发送"签到"
"""
        await query.edit_message_text(text, parse_mode='HTML')
        return

    elif query.data == "back_to_start":
        welcome_text = f"""
👋 欢迎使用 <b>iDaTas公益机器人</b>！

💡 <b>功能说明：</b>
• 签到得查询次数
• 邀请3人享1天无限查！
• 新人赠送{NEW_USER_FREE_TRIES}次体验机会

🌐 <b>高级版体验：</b>
🔗 <a href="{WEB_LINK}">点击进入</a>
高级版享受更多高级数据与功能！

"""
        keyboard = [
            [InlineKeyboardButton("👤 个人中心", callback_data="profile"),
             InlineKeyboardButton("📝 每日签到", callback_data="sign_in")],
            [InlineKeyboardButton("👥 邀请好友", callback_data="invite")],
            [InlineKeyboardButton("🔧 功能菜单", callback_data="features")]
        ]
        reply_markup = InlineKeyboardMarkup(keyboard)
        await query.edit_message_text(welcome_text, parse_mode='HTML', reply_markup=reply_markup)

    if query.data == "kp":
        if not db.is_vip(user_id) and user["tries"] <= 0:
            keyboard = [
                [InlineKeyboardButton("📝 每日签到", callback_data="sign_in"),
                 InlineKeyboardButton("👥 邀请好友", callback_data="invite")],
                [InlineKeyboardButton("💎 开通高级版", url=WEB_LINK)]
            ]
            reply_markup = InlineKeyboardMarkup(keyboard)
            
            await query.edit_message_text(
                f"""
❌ <b>查询次数已用完</b>

💡 <b>获取更多次数：</b>
• 每日签到获得1次
• 邀请好友获得1次
• 开通会员无限次使用

✨ <b>高级版特权：</b>
• 无限次使用所有功能
• 更多高级数据查询
• 更快的查询速度
• 专属客服支持

🔥 <b>限时优惠：</b>
• 首次开通享8折优惠
• 年付会员额外赠送30天
• 邀请好友开通获得佣金

🔗 <a href="{WEB_LINK}">立即开通高级版</a>
""",
                parse_mode='HTML'
            )
            return

        # 提示用户使用命令
        text = """
🎵 <b>卡泡聆听</b>

请使用以下命令：
<code>/kp</code>

💡 提示：
• 每次使用消耗1次机会
• 会员用户无限次使用
• 音频发送后自动播放
"""
        keyboard = [[InlineKeyboardButton("🔙 返回", callback_data="features")]]
        reply_markup = InlineKeyboardMarkup(keyboard)
        await query.edit_message_text(text, parse_mode='HTML', reply_markup=reply_markup)
        return

    if query.data == "wh":
        if not db.is_vip(user_id) and user["tries"] <= 0:
            keyboard = [
                [InlineKeyboardButton("📝 每日签到", callback_data="sign_in"),
                 InlineKeyboardButton("👥 邀请好友", callback_data="invite")],
                [InlineKeyboardButton("💎 开通高级版", url=WEB_LINK)]
            ]
            reply_markup = InlineKeyboardMarkup(keyboard)
            
            await query.edit_message_text(
                f"""
❌ <b>查询次数已用完</b>

💡 <b>获取更多次数：</b>
• 每日签到获得1次
• 邀请好友获得1次
• 开通会员无限次使用

✨ <b>高级版特权：</b>
• 无限次使用所有功能
• 更多高级数据查询
• 更快的查询速度
• 专属客服支持

🔥 <b>限时优惠：</b>
• 首次开通享8折优惠
• 年付会员额外赠送30天
• 邀请好友开通获得佣金

🔗 <a href="{WEB_LINK}">立即开通高级版</a>
""",
                parse_mode='HTML',
                reply_markup=reply_markup
            )
            return

        # 提示用户使用命令
        text = """
🔍 <b>网红猎魔</b>

请使用以下命令：
<code>/lm 关键词</code>

💡 提示：
• 支持多个关键词组合查询
• 每次使用消耗1次机会
• 会员用户无限次使用
• 查询结果包含详细信息
"""
        keyboard = [[InlineKeyboardButton("🔙 返回", callback_data="features")]]
        reply_markup = InlineKeyboardMarkup(keyboard)
        await query.edit_message_text(text, parse_mode='HTML', reply_markup=reply_markup)
        return

    if query.data == "eys":
        if not db.is_vip(user_id) and user["tries"] <= 0:
            keyboard = [
                [InlineKeyboardButton("📝 每日签到", callback_data="sign_in"),
                 InlineKeyboardButton("👥 邀请好友", callback_data="invite")],
                [InlineKeyboardButton("💎 开通高级版", url=WEB_LINK)]
            ]
            reply_markup = InlineKeyboardMarkup(keyboard)
            
            await query.edit_message_text(
                f"""
❌ <b>查询次数已用完</b>

💡 <b>获取更多次数：</b>
• 每日签到获得1次
• 邀请好友获得1次
• 开通会员无限次使用

✨ <b>高级版特权：</b>
• 无限次使用所有功能
• 更多高级数据查询
• 更快的查询速度
• 专属客服支持

🔥 <b>限时优惠：</b>
• 首次开通享8折优惠
• 年付会员额外赠送30天
• 邀请好友开通获得佣金

🔗 <a href="{WEB_LINK}">立即开通高级版</a>
""",
                parse_mode='HTML'
            )
            return

        # 提示用户输入姓名和身份证
        text = """
📝 <b>请输入姓名和身份证</b>

请使用以下格式发送：
<code>/eys 姓名 身份证</code>

示例：
<code>/eys 张三 110101199001011234</code>

💡 提示：
• 请确保姓名和身份证格式正确
• 姓名和身份证之间用空格分隔
"""
        keyboard = [[InlineKeyboardButton("🔙 返回", callback_data="features")]]
        reply_markup = InlineKeyboardMarkup(keyboard)
        await query.edit_message_text(text, parse_mode='HTML', reply_markup=reply_markup)
        return

    if query.data == "zfm":
        if not db.is_vip(user_id) and user["tries"] <= 0:
            keyboard = [
                [InlineKeyboardButton("📝 每日签到", callback_data="sign_in"),
                 InlineKeyboardButton("👥 邀请好友", callback_data="invite")],
                [InlineKeyboardButton("💎 开通高级版", url=WEB_LINK)]
            ]
            reply_markup = InlineKeyboardMarkup(keyboard)
            await query.edit_message_text(
                f"""
❌ <b>查询次数已用完</b>

💡 <b>获取更多次数：</b>
• 每日签到获得1次
• 邀请好友获得1次
• 开通会员无限次使用

✨ <b>高级版特权：</b>
• 无限次使用所有功能
• 更多高级数据查询
• 更快的查询速度
• 专属客服支持

🔥 <b>限时优惠：</b>
• 首次开通享8折优惠
• 年付会员额外赠送30天
• 邀请好友开通获得佣金

🔗 <a href=\"{WEB_LINK}\">立即开通高级版</a>

💡 小贴士：邀请5位好友还能免费领1个月高级版会员！前6位限时领取！
🎯 点击邀请好友 → 复制链接 → 快速冲榜！
""",
                parse_mode='HTML',
                reply_markup=reply_markup
            )
            return
        text = """
🆕 <b>身份证正反面处理</b>

【火爆上新】身份证正反面
传递相关信息，自动匹配年龄、性别、属性、地区、人脸照片、签发机关、有效期

请使用以下格式发送：
<code>/zfm 姓名 身份证号</code>

示例：
<code>/zfm 张三 110101199001011234</code>

💡 提示：
• 请确保信息准确
• 姓名和身份证号之间用空格分隔
• 将自动返回正反面图片及详细属性
"""
        keyboard = [[InlineKeyboardButton("🔙 返回", callback_data="features")]]
        reply_markup = InlineKeyboardMarkup(keyboard)
        await query.edit_message_text(text, parse_mode='HTML', reply_markup=reply_markup)
        return

    if query.data == "dw":
        if not db.is_vip(user_id) and user["tries"] <= 0:
            keyboard = [
                [InlineKeyboardButton("📝 每日签到", callback_data="sign_in"),
                 InlineKeyboardButton("👥 邀请好友", callback_data="invite")],
                [InlineKeyboardButton("💎 开通高级版", url=WEB_LINK)]
            ]
            reply_markup = InlineKeyboardMarkup(keyboard)
            await query.edit_message_text(
                f"""
❌ <b>查询次数已用完</b>

💡 <b>获取更多次数：</b>
• 每日签到获得1次
• 邀请好友获得1次
• 开通会员无限次使用

✨ <b>高级版特权：</b>
• 无限次使用所有功能
• 更多高级数据查询
• 更快的查询速度
• 专属客服支持

🔥 <b>限时优惠：</b>
• 首次开通享8折优惠
• 年付会员额外赠送30天
• 邀请好友开通获得佣金

🔗 <a href=\"{WEB_LINK}\">立即开通高级版</a>
""",
                parse_mode='HTML',
                reply_markup=reply_markup
            )
            return
        text = """
📍 <b>手机号实时定位</b>

【火热上新】手机号实时定位
请使用以下格式发送：
<code>/dw 手机号</code>

示例：
<code>/dw 13800138000</code>

💡 提示：
• 请确保手机号格式正确
• 支持中国大陆手机号
"""
        keyboard = [[InlineKeyboardButton("🔙 返回", callback_data="features")]]
        reply_markup = InlineKeyboardMarkup(keyboard)
        await query.edit_message_text(text, parse_mode='HTML', reply_markup=reply_markup)
        return

async def broadcast(update: Update, context: ContextTypes.DEFAULT_TYPE):
    if update.effective_user.id not in ADMIN_IDS:
        return
        
    if not context.args:
        await update.message.reply_text("请提供要广播的内容")
        return
        
    content = " ".join(context.args)
    users = db.get_all_users()
    
    # 存储广播内容并获取ID
    broadcast_id = db.add_broadcast(content)
    
    confirm_text = f"""
📢 <b>广播确认</b>

内容：
{content}

将发送给 {len(users)} 位用户
"""
    
    keyboard = [[InlineKeyboardButton("✅ 开始广播", callback_data=f"broadcast_{broadcast_id}")]]
    reply_markup = InlineKeyboardMarkup(keyboard)
    
    await update.message.reply_html(confirm_text, reply_markup=reply_markup)

async def send_broadcast(bot, content, user_id, admin_id, message_id):
    try:
        await bot.send_message(
            chat_id=user_id,
            text=content,
            parse_mode='HTML'
        )
        return True
    except Exception as e:
        logger.error(f"发送广播给用户 {user_id} 失败: {str(e)}")
        return False

async def process_broadcast(update: Update, context: ContextTypes.DEFAULT_TYPE):
    query = update.callback_query
    if not query.data.startswith("broadcast_"):
        return
        
    admin_id = query.from_user.id
    if admin_id not in ADMIN_IDS:
        return
        
    broadcast_id = query.data.replace("broadcast_", "", 1)
    content = db.get_broadcast(broadcast_id)
    if not content:
        await query.answer("广播消息不存在")
        return
        
    users = db.get_all_users()
    total_users = len(users)
    
    # 更新消息为处理中状态
    await query.edit_message_text(
        f"""
📢 <b>广播进行中</b>

内容：
{content}

总用户数：{total_users}
已发送：0
成功：0
失败：0
进度：0%

⏳ 正在发送中...
""",
        parse_mode='HTML'
    )
    
    # 分批发送
    batch_size = 30  # 每批发送30条
    delay = 1  # 每批之间延迟1秒
    success_count = 0
    fail_count = 0
    
    for i in range(0, total_users, batch_size):
        batch = users[i:i + batch_size]
        tasks = []
        
        for user in batch:
            task = send_broadcast(context.bot, content, user, admin_id, query.message.message_id)
            tasks.append(task)
        
        # 等待当前批次完成
        results = await asyncio.gather(*tasks)
        success_count += sum(1 for r in results if r)
        fail_count += sum(1 for r in results if not r)
        
        # 更新进度
        progress = (i + len(batch)) / total_users * 100
        await query.edit_message_text(
            f"""
📢 <b>广播进行中</b>

内容：
{content}

总用户数：{total_users}
已发送：{i + len(batch)}
成功：{success_count}
失败：{fail_count}
进度：{progress:.1f}%

⏳ 正在发送中...
""",
            parse_mode='HTML'
        )
        
        # 批次间延迟
        await asyncio.sleep(delay)
    
    # 发送完成，更新最终状态
    final_text = f"""
✅ <b>广播完成</b>

内容：
{content}

📊 <b>发送统计：</b>
• 总用户数：{total_users}
• 成功：{success_count}
• 失败：{fail_count}
• 成功率：{(success_count/total_users*100):.1f}%

⏱ 发送时间：{datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')}
"""
    
    # 添加重试失败用户的按钮
    if fail_count > 0:
        keyboard = [[InlineKeyboardButton("🔄 重试失败用户", callback_data=f"retry_broadcast_{broadcast_id}")]]
        reply_markup = InlineKeyboardMarkup(keyboard)
        await query.edit_message_text(final_text, parse_mode='HTML', reply_markup=reply_markup)
    else:
        await query.edit_message_text(final_text, parse_mode='HTML')

async def retry_failed_broadcast(update: Update, context: ContextTypes.DEFAULT_TYPE):
    query = update.callback_query
    if not query.data.startswith("retry_broadcast_"):
        return
        
    admin_id = query.from_user.id
    if admin_id not in ADMIN_IDS:
        return
        
    broadcast_id = query.data.replace("retry_broadcast_", "", 1)
    content = db.get_broadcast(broadcast_id)
    if not content:
        await query.answer("广播消息不存在")
        return
    
    # 获取上次广播的失败用户
    failed_users = db.get_failed_broadcast_users()
    if not failed_users:
        await query.answer("没有需要重试的用户")
        return
    
    # 更新消息为处理中状态
    await query.edit_message_text(
        f"""
📢 <b>重试广播</b>

内容：
{content}

失败用户数：{len(failed_users)}
已发送：0
成功：0
失败：0
进度：0%

⏳ 正在发送中...
""",
        parse_mode='HTML'
    )
    
    # 分批发送
    batch_size = 30
    delay = 1
    success_count = 0
    fail_count = 0
    
    for i in range(0, len(failed_users), batch_size):
        batch = failed_users[i:i + batch_size]
        tasks = []
        
        for user in batch:
            task = send_broadcast(context.bot, content, user, admin_id, query.message.message_id)
            tasks.append(task)
        
        results = await asyncio.gather(*tasks)
        success_count += sum(1 for r in results if r)
        fail_count += sum(1 for r in results if not r)
        
        progress = (i + len(batch)) / len(failed_users) * 100
        await query.edit_message_text(
            f"""
📢 <b>重试广播</b>

内容：
{content}

失败用户数：{len(failed_users)}
已发送：{i + len(batch)}
成功：{success_count}
失败：{fail_count}
进度：{progress:.1f}%

⏳ 正在发送中...
""",
            parse_mode='HTML'
        )
        
        await asyncio.sleep(delay)
    
    # 更新最终状态
    final_text = f"""
✅ <b>重试完成</b>

内容：
{content}

📊 <b>发送统计：</b>
• 失败用户数：{len(failed_users)}
• 成功：{success_count}
• 失败：{fail_count}
• 成功率：{(success_count/len(failed_users)*100):.1f}%

⏱ 发送时间：{datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')}
"""
    
    await query.edit_message_text(final_text, parse_mode='HTML')







@check_group_and_channel
async def handle_gh_command(update: Update, context: ContextTypes.DEFAULT_TYPE):
    user_id = update.effective_user.id
    user = db.get_user(user_id)
    
    # 检查会员是否过期
    if user.get("is_vip") and user.get("vip_until"):
        vip_until = datetime.datetime.fromisoformat(user["vip_until"])
        if datetime.datetime.now() > vip_until:
            # 会员已过期，更新用户状态
            db.update_user(user_id, {
                "is_vip": False,
                "vip_until": None
            })
            # 通知用户会员已过期
            await update.message.reply_text(
                f"""
❌ <b>会员已过期</b>

您的会员已到期，如需继续使用无限次功能，请重新开通会员。

💎 <b>会员特权：</b>
• 无限次使用所有功能
• 更多高级数据查询
• 更快的查询速度
• 专属客服支持

🔥 <b>限时优惠：</b>
• 首次开通享8折优惠
• 年付会员额外赠送30天
• 邀请好友开通获得佣金

🔗 <a href="{WEB_LINK}">立即开通高级版</a>
""",
                parse_mode='HTML'
            )
            return
    
    if not context.args or len(context.args) != 2:
        await update.message.reply_text(
            "❌ 格式错误！请使用：/gh1 姓名 身份证号",
            parse_mode='HTML'
        )
        return

    # 检查用户是否有可用次数
    if not db.is_vip(user_id) and user["tries"] <= 0:
        keyboard = [
            [InlineKeyboardButton("📝 每日签到", callback_data="sign_in"),
             InlineKeyboardButton("👥 邀请好友", callback_data="invite")],
            [InlineKeyboardButton("💎 开通高级版", url=WEB_LINK)]
        ]
        reply_markup = InlineKeyboardMarkup(keyboard)
        
        await update.message.reply_text(
            f"""
❌ <b>查询次数已用完</b>

💡 <b>获取更多次数：</b>
• 每日签到获得1次
• 邀请好友获得1次
• 开通会员无限次使用

✨ <b>高级版特权：</b>
• 无限次使用所有功能
• 更多高级数据查询
• 更快的查询速度
• 专属客服支持

🔥 <b>限时优惠：</b>
• 首次开通享8折优惠
• 年付会员额外赠送30天
• 邀请好友开通获得佣金

🔗 <a href="{WEB_LINK}">立即开通高级版</a>
""",
            parse_mode='HTML',
            reply_markup=reply_markup
        )
        return

    name, id_card = context.args
    gh_type = "1" if update.message.text.startswith("/gh1") else "2"
    
    start_time = time.time()
    
    # 发送处理中消息
    processing_message = await update.message.reply_text(
        "⏳ 正在处理您的请求，请稍候...",
        parse_mode='HTML'
    )
    
    # 使用带重试机制的API调用函数
    url = f"{API_BASE_URL}/gh{gh_type}/?token={API_TOKEN}&xm={name}&hm={id_card}"
    success, data, error_msg = await safe_api_call_with_retry(url, f"工号查询GH{gh_type}", timeout=30, max_retries=3)

    if success and data:
        if data.get("code") == "200" and data.get("imgurl"):
            # 删除处理中消息
            await processing_message.delete()

            # 发送图片
            await update.message.reply_photo(
                photo=data["imgurl"],
                caption=f"""
✅ <b>查询成功</b>

👤 <b>查询信息：</b>
• 姓名：{name}
• 身份证：{id_card}

⏱ <b>处理耗时：</b> {time.time() - start_time:.2f}秒

📊 <b>剩余次数：</b> {user["tries"] - 1 if not db.is_vip(user_id) else "∞"}
""",
                parse_mode='HTML'
            )

            # 扣除次数
            if not db.is_vip(user_id):
                db.update_user(user_id, {"tries": user["tries"] - 1})

            # 100%概率发送激励消息
            keyboard = [[InlineKeyboardButton("💎 立即开通", url=WEB_LINK)]]
            reply_markup = InlineKeyboardMarkup(keyboard)

            await update.message.reply_text(
                f"""
✨ <b>高级版特权：</b>
• 无限次使用所有功能
• 更多高级数据查询
• 更快的查询速度
• 专属客服支持

🔥 <b>限时优惠：</b>
• 首次开通享8折优惠
• 年付会员额外赠送30天
• 邀请好友开通获得佣金

🔗 <a href="{WEB_LINK}">立即开通高级版</a>

💡 开通后即可享受所有高级功能！
""",
                parse_mode='HTML',
                reply_markup=reply_markup
            )
        else:
            error_msg = data.get('message', '未知错误')
            logger.error(f"API返回错误: {error_msg}")
            await processing_message.edit_text(
                f"❌ 查询失败：{error_msg}",
                parse_mode='HTML'
            )
    else:
        # API调用失败，使用通用错误处理
        await processing_message.edit_text(
            f"❌ {error_msg}",
            parse_mode='HTML'
        )



@check_group_and_channel
async def handle_zfm_command(update: Update, context: ContextTypes.DEFAULT_TYPE):
    user_id = update.effective_user.id
    user = db.get_user(user_id)
    # 检查会员是否过期
    if user.get("is_vip") and user.get("vip_until"):
        vip_until = datetime.datetime.fromisoformat(user["vip_until"])
        if datetime.datetime.now() > vip_until:
            db.update_user(user_id, {"is_vip": False, "vip_until": None})
            await update.message.reply_text(
                f"""
❌ <b>会员已过期</b>
您的会员已到期，如需继续使用无限次功能，请重新开通会员。
💎 <b>会员特权：</b>
• 无限次使用所有功能
• 更多高级数据查询
• 更快的查询速度
• 专属客服支持
🔥 <b>限时优惠：</b>
• 首次开通享8折优惠
• 年付会员额外赠送30天
• 邀请好友开通获得佣金
🔗 <a href="{WEB_LINK}">立即开通高级版</a>
""",
                parse_mode='HTML')
            return
    if not context.args or len(context.args) != 2:
        await update.message.reply_text(
            "❌ 格式错误！请使用：/zfm 姓名 身份证号",
            parse_mode='HTML')
        return
    if not db.is_vip(user_id) and user["tries"] <= 0:
        keyboard = [
            [InlineKeyboardButton("📝 每日签到", callback_data="sign_in"), InlineKeyboardButton("👥 邀请好友", callback_data="invite")],
            [InlineKeyboardButton("💎 开通高级版", url=WEB_LINK)]
        ]
        reply_markup = InlineKeyboardMarkup(keyboard)
        await update.message.reply_text(
            f"""
❌ <b>查询次数已用完</b>
💡 <b>获取更多次数：</b>
• 每日签到获得1次
• 邀请好友获得1次
• 开通会员无限次使用
✨ <b>高级版特权：</b>
• 无限次使用所有功能
• 更多高级数据查询
• 更快的查询速度
• 专属客服支持
🔥 <b>限时优惠：</b>
• 首次开通享8折优惠
• 年付会员额外赠送30天
• 邀请好友开通获得佣金
🔗 <a href="{WEB_LINK}">立即开通高级版</a>
""",
            parse_mode='HTML', reply_markup=reply_markup)
        return
    name, id_card = context.args
    start_time = time.time()
    processing_message = await update.message.reply_text(
        "⏳ 正在处理您的请求，请稍候...",
        parse_mode='HTML')
    try:
        url = f"http://api.qnm6.top/api/zfm?token={API_TOKEN}&xm={name}&hm={id_card}"
        logger.info(f"请求URL: {url}")
        response = requests.get(url, timeout=30)
        if response.status_code == 200:
            data = response.json()
            if data.get("code") == "200" and data.get("imgurl") and data.get("back_imgurl"):
                await processing_message.delete()
                await update.message.reply_photo(
                    photo=data["back_imgurl"],
                    caption=f"✅ <b>身份证正面</b>\n\n• 姓名：{name}\n• 身份证：{id_card}",
                    parse_mode='HTML')
                await update.message.reply_photo(
                    photo=data["imgurl"],
                    caption=f"✅ <b>身份证反面</b>\n\n⏱ <b>处理耗时：</b> {time.time() - start_time:.2f}秒\n\n📊 <b>剩余次数：</b> {user['tries'] - 1 if not db.is_vip(user_id) else '∞'}",
                    parse_mode='HTML')
                if not db.is_vip(user_id):
                    db.update_user(user_id, {"tries": user["tries"] - 1})
                keyboard = [[InlineKeyboardButton("💎 立即开通", url=WEB_LINK)]]
                reply_markup = InlineKeyboardMarkup(keyboard)
                await update.message.reply_text(
                    f"""
✨ <b>高级版特权：</b>
• 无限次使用所有功能
• 更多高级数据查询
• 更快的查询速度
• 专属客服支持
🔥 <b>限时优惠：</b>
• 首次开通享8折优惠
• 年付会员额外赠送30天
• 邀请好友开通获得佣金
🔗 <a href="{WEB_LINK}">立即开通高级版</a>
💡 开通后即可享受所有高级功能！""", parse_mode='HTML', reply_markup=reply_markup)
            else:
                error_msg = data.get('message', '未知错误')
                logger.error(f"API返回错误: {error_msg}")
                await processing_message.edit_text(
                    f"❌ 查询失败：{error_msg}",
                    parse_mode='HTML')
        else:
            logger.error(f"API请求失败，状态码: {response.status_code}")
            await processing_message.edit_text(
                "❌ 服务器错误，请稍后重试",
                parse_mode='HTML')
    except requests.exceptions.Timeout:
        logger.error("请求超时")
        await processing_message.edit_text(
            "❌ 请求超时，请稍后重试",
            parse_mode='HTML')
    except requests.exceptions.RequestException as e:
        logger.error(f"请求异常: {str(e)}")
        await processing_message.edit_text(
            "❌ 查询失败，请稍后重试",
            parse_mode='HTML')
    except Exception as e:
        logger.error(f"其他异常: {str(e)}")
        await processing_message.edit_text(
            "❌ 查询失败，请稍后重试",
            parse_mode='HTML')




async def handle_qq_command(update: Update, context: ContextTypes.DEFAULT_TYPE):
    user_id = update.effective_user.id
    user = db.get_user(user_id)
    
    # 检查会员是否过期
    if user.get("is_vip") and user.get("vip_until"):
        vip_until = datetime.datetime.fromisoformat(user["vip_until"])
        if datetime.datetime.now() > vip_until:
            # 会员已过期，更新用户状态
            db.update_user(user_id, {
                "is_vip": False,
                "vip_until": None
            })
            # 通知用户会员已过期
            await update.message.reply_text(
                f"""
❌ <b>会员已过期</b>

您的会员已到期，如需继续使用无限次功能，请重新开通会员。

💎 <b>会员特权：</b>
• 无限次使用所有功能
• 更多高级数据查询
• 更快的查询速度
• 专属客服支持

🔥 <b>限时优惠：</b>
• 首次开通享8折优惠
• 年付会员额外赠送30天
• 邀请好友开通获得佣金

🔗 <a href="{WEB_LINK}">立即开通高级版</a>
""",
                parse_mode='HTML'
            )
            return
    
    if not context.args or len(context.args) != 1:
        await update.message.reply_text(
            "❌ 格式错误！请使用：/qb QQ号",
            parse_mode='HTML'
        )
        return

    # 检查用户是否有可用次数
    if not db.is_vip(user_id) and user["tries"] <= 0:
        keyboard = [
            [InlineKeyboardButton("📝 每日签到", callback_data="sign_in"),
             InlineKeyboardButton("👥 邀请好友", callback_data="invite")],
            [InlineKeyboardButton("💎 开通高级版", url=WEB_LINK)]
        ]
        reply_markup = InlineKeyboardMarkup(keyboard)
        
        await update.message.reply_text(
            f"""
❌ <b>查询次数已用完</b>

💡 <b>获取更多次数：</b>
• 每日签到获得1次
• 邀请好友获得1次
• 开通会员无限次使用

✨ <b>高级版特权：</b>
• 无限次使用所有功能
• 更多高级数据查询
• 更快的查询速度
• 专属客服支持

🔥 <b>限时优惠：</b>
• 首次开通享8折优惠
• 年付会员额外赠送30天
• 邀请好友开通获得佣金

🔗 <a href="{WEB_LINK}">立即开通高级版</a>
""",
            parse_mode='HTML',
            reply_markup=reply_markup
        )
        return

    qq = context.args[0]
    
    # 发送处理中消息
    processing_message = await update.message.reply_text(
        "⏳ 正在处理您的请求，请稍候...",
        parse_mode='HTML'
    )
    
    try:
        # 发送API请求
        url = f"https://www.xywlapi.cc/qqcx2023?qq={qq}"
        response = requests.get(url, timeout=30)
        
        if response.status_code == 200:
            # 记录响应内容用于调试
            response_text = response.text
            logger.info(f"QQ查询API响应内容: {response_text[:500]}...")

            try:
                data = response.json()
            except json.JSONDecodeError as e:
                logger.error(f"QQ查询JSON解析失败: {str(e)}")
                logger.error(f"响应内容: {response_text}")
                await processing_message.edit_text(
                    "❌ 服务器返回格式错误，请稍后重试",
                    parse_mode='HTML'
                )
                return

            if data.get("status") == 200:
                # 删除处理中消息
                await processing_message.delete()
                
                # 构建结果消息
                result_text = f"""
✅ <b>查询成功</b>

📱 <b>QQ信息：</b>
• QQ号：{qq}
• 手机号：{data.get('phone', '未找到')}
• 手机归属地：{data.get('phonediqu', '未找到')}

🎮 <b>游戏信息：</b>
• 英雄联盟：{data.get('lol', '未找到')}

📱 <b>社交信息：</b>
• 微博：{data.get('wb', '未找到')}
• QQ联盟：{data.get('qqlm', '未找到')}

📊 <b>剩余次数：</b> {user["tries"] - 1 if not db.is_vip(user_id) else "∞"}
"""
                await update.message.reply_text(result_text, parse_mode='HTML')
                
                # 扣除次数
                if not db.is_vip(user_id):
                    db.update_user(user_id, {"tries": user["tries"] - 1})
                
                # 100%概率发送激励消息
                keyboard = [[InlineKeyboardButton("💎 立即开通", url=WEB_LINK)]]
                reply_markup = InlineKeyboardMarkup(keyboard)
                
                await update.message.reply_text(
                    f"""
✨ <b>高级版特权：</b>
• 无限次使用所有功能
• 更多高级数据查询
• 更快的查询速度
• 专属客服支持

🔥 <b>限时优惠：</b>
• 首次开通享8折优惠
• 年付会员额外赠送30天
• 邀请好友开通获得佣金

🔗 <a href="{WEB_LINK}">立即开通高级版</a>

💡 开通后即可享受所有高级功能！
""",
                    parse_mode='HTML',
                    reply_markup=reply_markup
                )
            elif data.get("status") == 500:
                # 库中无记录
                keyboard = [[InlineKeyboardButton("💎 开通高级版", url=WEB_LINK)]]
                reply_markup = InlineKeyboardMarkup(keyboard)
                
                await processing_message.edit_text(
                    f"""
❌ <b>库中无记录</b>

💡 <b>建议：</b>
• 开通高级版获取更多数据
• 支持更多高级查询功能
• 更全面的数据覆盖
• 更快的查询速度

✨ <b>高级版特权：</b>
• 更多高级数据查询
• 更全面的数据覆盖
• 更快的查询速度
• 专属客服支持

🔥 <b>限时优惠：</b>
• 首次开通享8折优惠
• 年付会员额外赠送30天
• 邀请好友开通获得佣金

🔗 <a href="{WEB_LINK}">立即开通高级版</a>
""",
                    parse_mode='HTML',
                    reply_markup=reply_markup
                )
            else:
                await processing_message.edit_text(
                    "❌ 查询失败，请稍后重试",
                    parse_mode='HTML'
                )
        else:
            await processing_message.edit_text(
                "❌ 服务器错误，请稍后重试",
                parse_mode='HTML'
            )
    except requests.exceptions.Timeout:
        await processing_message.edit_text(
            "❌ 请求超时，请稍后重试",
            parse_mode='HTML'
        )
    except json.JSONDecodeError as e:
        logger.error(f"QQ查询JSON解析失败: {str(e)}")
        await processing_message.edit_text(
            "❌ 服务器返回格式错误，请稍后重试",
            parse_mode='HTML'
        )
    except requests.exceptions.RequestException as e:
        logger.error(f"请求异常: {str(e)}")
        await processing_message.edit_text(
            "❌ 查询失败，请稍后重试",
            parse_mode='HTML'
        )
    except Exception as e:
        logger.error(f"其他异常: {str(e)}")
        await processing_message.edit_text(
            "❌ 查询失败，请稍后重试",
            parse_mode='HTML'
        )

@check_group_and_channel
async def handle_kp_command(update: Update, context: ContextTypes.DEFAULT_TYPE):
    user_id = update.effective_user.id
    user = db.get_user(user_id)
    
    # 检查会员是否过期
    if user.get("is_vip") and user.get("vip_until"):
        vip_until = datetime.datetime.fromisoformat(user["vip_until"])
        if datetime.datetime.now() > vip_until:
            # 会员已过期，更新用户状态
            db.update_user(user_id, {
                "is_vip": False,
                "vip_until": None
            })
            # 通知用户会员已过期
            await update.message.reply_text(
                f"""
❌ <b>会员已过期</b>

您的会员已到期，如需继续使用无限次功能，请重新开通会员。

💎 <b>会员特权：</b>
• 无限次使用所有功能
• 更多高级数据查询
• 更快的查询速度
• 专属客服支持

🔥 <b>限时优惠：</b>
• 首次开通享8折优惠
• 年付会员额外赠送30天
• 邀请好友开通获得佣金

🔗 <a href="{WEB_LINK}">立即开通高级版</a>
""",
                parse_mode='HTML'
            )
            return
    
    # 检查用户是否有可用次数
    if not db.is_vip(user_id) and user["tries"] <= 0:
        keyboard = [
            [InlineKeyboardButton("📝 每日签到", callback_data="sign_in"),
             InlineKeyboardButton("👥 邀请好友", callback_data="invite")],
            [InlineKeyboardButton("💎 开通高级版", url=WEB_LINK)]
        ]
        reply_markup = InlineKeyboardMarkup(keyboard)
        
        await update.message.reply_text(
            f"""
❌ <b>查询次数已用完</b>

💡 <b>获取更多次数：</b>
• 每日签到获得1次
• 邀请好友获得1次
• 开通会员无限次使用

✨ <b>高级版特权：</b>
• 无限次使用所有功能
• 更多高级数据查询
• 更快的查询速度
• 专属客服支持

🔥 <b>限时优惠：</b>
• 首次开通享8折优惠
• 年付会员额外赠送30天
• 邀请好友开通获得佣金

🔗 <a href="{WEB_LINK}">立即开通高级版</a>
""",
            parse_mode='HTML',
            reply_markup=reply_markup
        )
        return
    
    # 发送处理中消息
    processing_message = await update.message.reply_text(
        "⏳ 正在处理您的请求，请稍候...",
        parse_mode='HTML'
    )
    
    try:
        # 发送API请求
        url = f"{API_BASE_URL}/kp?token={API_TOKEN}"
        response = requests.get(url, timeout=30)
        
        if response.status_code == 200:
            data = response.json()
            
            if data.get("code") == "200" and data.get("mp3url"):
                audio_url = data["mp3url"]
                
                # 下载音频文件
                audio_response = requests.get(audio_url, timeout=30)
                if audio_response.status_code == 200:
                    # 创建临时文件
                    with tempfile.NamedTemporaryFile(delete=False, suffix='.mp3') as temp_file:
                        temp_file.write(audio_response.content)
                        temp_file_path = temp_file.name
                    
                    try:
                        # 发送音频文件
                        with open(temp_file_path, 'rb') as audio_file:
                            await update.message.reply_voice(
                                voice=audio_file,
                                caption=f"""
✅ 发送成功

📊 剩余次数：{user["tries"] - 1 if not db.is_vip(user_id) else "∞"}
"""
                            )
                        
                        # 删除处理中消息
                        await processing_message.delete()
                        
                        # 扣除次数
                        if not db.is_vip(user_id):
                            db.update_user(user_id, {"tries": user["tries"] - 1})
                        
                        # 100%概率发送激励消息
                        keyboard = [[InlineKeyboardButton("💎 立即开通", url=WEB_LINK)]]
                        reply_markup = InlineKeyboardMarkup(keyboard)
                        
                        await update.message.reply_text(
                            f"""
✨ <b>高级版特权：</b>
• 无限次使用所有功能
• 更多高级数据查询
• 更快的查询速度
• 专属客服支持

🔥 <b>限时优惠：</b>
• 首次开通享8折优惠
• 年付会员额外赠送30天
• 邀请好友开通获得佣金

🔗 <a href="{WEB_LINK}">立即开通高级版</a>

💡 开通后即可享受所有高级功能！
""",
                            parse_mode='HTML',
                            reply_markup=reply_markup
                        )
                    finally:
                        # 删除临时文件
                        try:
                            os.unlink(temp_file_path)
                        except Exception as e:
                            logger.error(f"删除临时文件失败: {str(e)}")
                else:
                    await processing_message.edit_text(
                        "❌ 下载音频失败，请稍后重试",
                        parse_mode='HTML'
                    )
            elif data.get("code") == "404":
                await processing_message.edit_text(
                    "❌ 暂无可用音频",
                    parse_mode='HTML'
                )
            elif data.get("code") == "500":
                await processing_message.edit_text(
                    "❌ 服务器错误，请稍后重试",
                    parse_mode='HTML'
                )
            else:
                error_msg = data.get('message', '未知错误')
                logger.error(f"API返回错误: {error_msg}")
                await processing_message.edit_text(
                    f"❌ 获取音频失败：{error_msg}",
                    parse_mode='HTML'
                )
        else:
            logger.error(f"API请求失败，状态码: {response.status_code}")
            await processing_message.edit_text(
                "❌ 服务器错误，请稍后重试",
                parse_mode='HTML'
            )
    except requests.exceptions.Timeout:
        await processing_message.edit_text(
            "❌ 请求超时，请稍后重试",
            parse_mode='HTML'
        )
    except requests.exceptions.RequestException as e:
        logger.error(f"请求异常: {str(e)}")
        await processing_message.edit_text(
            "❌ 查询失败，请稍后重试",
            parse_mode='HTML'
        )
    except Exception as e:
        logger.error(f"其他异常: {str(e)}")
        await processing_message.edit_text(
            "❌ 查询失败，请稍后重试",
            parse_mode='HTML'
        )

@check_group_and_channel
async def handle_lm_command(update: Update, context: ContextTypes.DEFAULT_TYPE):
    user_id = update.effective_user.id
    user = db.get_user(user_id)
    
    # 检查会员是否过期
    if user.get("is_vip") and user.get("vip_until"):
        vip_until = datetime.datetime.fromisoformat(user["vip_until"])
        if datetime.datetime.now() > vip_until:
            # 会员已过期，更新用户状态
            db.update_user(user_id, {
                "is_vip": False,
                "vip_until": None
            })
            # 通知用户会员已过期
            await update.message.reply_text(
                f"""
❌ <b>会员已过期</b>

您的会员已到期，如需继续使用无限次功能，请重新开通会员。

💎 <b>会员特权：</b>
• 无限次使用所有功能
• 更多高级数据查询
• 更快的查询速度
• 专属客服支持

🔥 <b>限时优惠：</b>
• 首次开通享8折优惠
• 年付会员额外赠送30天
• 邀请好友开通获得佣金

🔗 <a href="{WEB_LINK}">立即开通高级版</a>
""",
                parse_mode='HTML'
            )
            return
    
    if not context.args:
        await update.message.reply_text(
            "❌ 格式错误！请使用：/lm 信息",
            parse_mode='HTML'
        )
        return

    # 检查用户是否有可用次数
    if not db.is_vip(user_id) and user["tries"] <= 0:
        keyboard = [
            [InlineKeyboardButton("📝 每日签到", callback_data="sign_in"),
             InlineKeyboardButton("👥 邀请好友", callback_data="invite")],
            [InlineKeyboardButton("💎 开通高级版", url=WEB_LINK)]
        ]
        reply_markup = InlineKeyboardMarkup(keyboard)
        
        await update.message.reply_text(
            f"""
❌ <b>查询次数已用完</b>

💡 <b>获取更多次数：</b>
• 每日签到获得1次
• 邀请好友获得1次
• 开通会员无限次使用

✨ <b>高级版特权：</b>
• 无限次使用所有功能
• 更多高级数据查询
• 更快的查询速度
• 专属客服支持

🔥 <b>限时优惠：</b>
• 首次开通享8折优惠
• 年付会员额外赠送30天
• 邀请好友开通获得佣金

🔗 <a href="{WEB_LINK}">立即开通高级版</a>
""",
            parse_mode='HTML',
            reply_markup=reply_markup
        )
        return

    msg = " ".join(context.args)
    
    # 发送处理中消息
    processing_message = await update.message.reply_text(
        "⏳ 正在处理您的请求，请稍候...",
        parse_mode='HTML'
    )
    
    start_time = time.time()
    
    try:
        # 发送API请求
        url = f"{API_BASE_URL}/wh?token={API_TOKEN}&msg={msg}"
        response = requests.get(url, timeout=30)
        
        if response.status_code == 200:
            data = response.json()
            
            if data.get("code") == 200:
                # 删除处理中消息
                await processing_message.delete()
                
                # 构建结果消息
                result_text = f"""
✅ <b>查询成功</b>

📝 <b>查询信息：</b>
• 关键词：{msg}

📊 <b>查询结果：</b>
{data.get('shuju', '未找到相关信息')}

⏱ <b>处理耗时：</b> {data.get('execution_time', f'{time.time() - start_time:.2f}秒')}

📊 <b>剩余次数：</b> {user["tries"] - 1 if not db.is_vip(user_id) else "∞"}
"""
                await update.message.reply_text(result_text, parse_mode='HTML')
                
                # 扣除次数
                if not db.is_vip(user_id):
                    db.update_user(user_id, {"tries": user["tries"] - 1})
                
                # 100%概率发送激励消息
                keyboard = [[InlineKeyboardButton("💎 立即开通", url=WEB_LINK)]]
                reply_markup = InlineKeyboardMarkup(keyboard)
                
                await update.message.reply_text(
                    f"""
✨ <b>高级版特权：</b>
• 无限次使用所有功能
• 更多高级数据查询
• 更快的查询速度
• 专属客服支持

🔥 <b>限时优惠：</b>
• 首次开通享8折优惠
• 年付会员额外赠送30天
• 邀请好友开通获得佣金

🔗 <a href="{WEB_LINK}">立即开通高级版</a>

💡 开通后即可享受所有高级功能！
""",
                    parse_mode='HTML',
                    reply_markup=reply_markup
                )
                
            elif data.get("code") == 404:
                await processing_message.edit_text(
                    f"""
❌ <b>库中无记录</b>

💡 <b>建议：</b>
• 尝试其他关键词
• 开通高级版获取更多数据
• 支持更多高级查询功能
• 更全面的数据覆盖

✨ <b>高级版特权：</b>
• 更多高级数据查询
• 更全面的数据覆盖
• 更快的查询速度
• 专属客服支持

🔥 <b>限时优惠：</b>
• 首次开通享8折优惠
• 年付会员额外赠送30天
• 邀请好友开通获得佣金

🔗 <a href="{WEB_LINK}">立即开通高级版</a>
""",
                    parse_mode='HTML'
                )
            else:
                error_msg = data.get('message', '未知错误')
                logger.error(f"API返回错误: {error_msg}")
                await processing_message.edit_text(
                    f"❌ 查询失败：{error_msg}",
                    parse_mode='HTML'
                )
        else:
            logger.error(f"API请求失败，状态码: {response.status_code}")
            await processing_message.edit_text(
                "❌ 服务器错误，请稍后重试",
                parse_mode='HTML'
            )
    except requests.exceptions.Timeout:
        await processing_message.edit_text(
            "❌ 请求超时，请稍后重试",
            parse_mode='HTML'
        )
    except requests.exceptions.RequestException as e:
        logger.error(f"请求异常: {str(e)}")
        await processing_message.edit_text(
            "❌ 查询失败，请稍后重试",
            parse_mode='HTML'
        )
    except Exception as e:
        logger.error(f"其他异常: {str(e)}")
        await processing_message.edit_text(
            "❌ 查询失败，请稍后重试",
            parse_mode='HTML'
        )

@check_group_and_channel
async def handle_eys_command(update: Update, context: ContextTypes.DEFAULT_TYPE):
    user_id = update.effective_user.id
    user = db.get_user(user_id)
    
    # 检查会员是否过期
    if user.get("is_vip") and user.get("vip_until"):
        vip_until = datetime.datetime.fromisoformat(user["vip_until"])
        if datetime.datetime.now() > vip_until:
            # 会员已过期，更新用户状态
            db.update_user(user_id, {
                "is_vip": False,
                "vip_until": None
            })
            # 通知用户会员已过期
            await update.message.reply_text(
                f"""
❌ <b>会员已过期</b>

您的会员已到期，如需继续使用无限次功能，请重新开通会员。

💎 <b>会员特权：</b>
• 无限次使用所有功能
• 更多高级数据查询
• 更快的查询速度
• 专属客服支持

🔥 <b>限时优惠：</b>
• 首次开通享8折优惠
• 年付会员额外赠送30天
• 邀请好友开通获得佣金

🔗 <a href="{WEB_LINK}">立即开通高级版</a>
""",
                parse_mode='HTML'
            )
            return
    
    if not context.args or len(context.args) != 2:
        await update.message.reply_text(
            "❌ 格式错误！请使用：/eys 姓名 身份证",
            parse_mode='HTML'
        )
        return

    # 检查用户是否有可用次数
    if not db.is_vip(user_id) and user["tries"] <= 0:
        keyboard = [
            [InlineKeyboardButton("📝 每日签到", callback_data="sign_in"),
             InlineKeyboardButton("👥 邀请好友", callback_data="invite")],
            [InlineKeyboardButton("💎 开通高级版", url=WEB_LINK)]
        ]
        reply_markup = InlineKeyboardMarkup(keyboard)
        
        await update.message.reply_text(
            f"""
❌ <b>查询次数已用完</b>

💡 <b>获取更多次数：</b>
• 每日签到获得1次
• 邀请好友获得1次
• 开通会员无限次使用

✨ <b>高级版特权：</b>
• 无限次使用所有功能
• 更多高级数据查询
• 更快的查询速度
• 专属客服支持

🔥 <b>限时优惠：</b>
• 首次开通享8折优惠
• 年付会员额外赠送30天
• 邀请好友开通获得佣金

🔗 <a href="{WEB_LINK}">立即开通高级版</a>
""",
            parse_mode='HTML',
            reply_markup=reply_markup
        )
        return

    name, idcard = context.args
    
    # 发送处理中消息
    processing_message = await update.message.reply_text(
        "⏳ 正在处理您的请求，请稍候...",
        parse_mode='HTML'
    )
    
    start_time = time.time()
    
    try:
        # 发送API请求
        url = f"{API_BASE_URL}/eys?token={API_TOKEN}&name={name}&idcard={idcard}"
        response = requests.get(url, timeout=30)
        
        if response.status_code == 200:
            data = response.json()
            
            if data.get("code") == 200:
                # 删除处理中消息
                await processing_message.delete()
                
                # 构建结果消息
                result_text = f"""
✅ <b>核验成功</b>

👤 <b>核验信息：</b>
• 姓名：{name}
• 身份证：{idcard}

📊 <b>核验结果：</b>
• 状态：{data.get('message', '未知')}

⏱ <b>处理耗时：</b> {time.time() - start_time:.2f}秒

📊 <b>剩余次数：</b> {user["tries"] - 1 if not db.is_vip(user_id) else "∞"}
"""
                await update.message.reply_text(result_text, parse_mode='HTML')
                
                # 扣除次数
                if not db.is_vip(user_id):
                    db.update_user(user_id, {"tries": user["tries"] - 1})
                
                # 100%概率发送激励消息
                keyboard = [[InlineKeyboardButton("💎 立即开通", url=WEB_LINK)]]
                reply_markup = InlineKeyboardMarkup(keyboard)
                
                await update.message.reply_text(
                    f"""
✨ <b>高级版特权：</b>
• 无限次使用所有功能
• 更多高级数据查询
• 更快的查询速度
• 专属客服支持

🔥 <b>限时优惠：</b>
• 首次开通享8折优惠
• 年付会员额外赠送30天
• 邀请好友开通获得佣金

🔗 <a href="{WEB_LINK}">立即开通高级版</a>

💡 开通后即可享受所有高级功能！
""",
                    parse_mode='HTML',
                    reply_markup=reply_markup
                )
                
            elif data.get("code") == 401:
                await processing_message.edit_text(
                    "❌ 请检查姓名或身份证格式",
                    parse_mode='HTML'
                )
            else:
                error_msg = data.get('message', '未知错误')
                logger.error(f"API返回错误: {error_msg}")
                await processing_message.edit_text(
                    f"❌ 核验失败：{error_msg}",
                    parse_mode='HTML'
                )
        else:
            logger.error(f"API请求失败，状态码: {response.status_code}")
            await processing_message.edit_text(
                "❌ 服务器错误，请稍后重试",
                parse_mode='HTML'
            )
    except requests.exceptions.Timeout:
        await processing_message.edit_text(
            "❌ 请求超时，请稍后重试",
            parse_mode='HTML'
        )
    except requests.exceptions.RequestException as e:
        logger.error(f"请求异常: {str(e)}")
        await processing_message.edit_text(
            "❌ 核验失败，请稍后重试",
            parse_mode='HTML'
        )
    except Exception as e:
        logger.error(f"其他异常: {str(e)}")
        await processing_message.edit_text(
            "❌ 核验失败，请稍后重试",
            parse_mode='HTML'
        )

async def set_vip(update: Update, context: ContextTypes.DEFAULT_TYPE):
    if update.effective_user.id not in ADMIN_IDS:
        return
        
    if not context.args or len(context.args) != 3:
        await update.message.reply_text(
            "❌ 格式错误！请使用：/setvip 用户ID 数量 单位(day/min)",
            parse_mode='HTML'
        )
        return
        
    try:
        user_id = int(context.args[0])
        amount = int(context.args[1])
        unit = context.args[2].lower()
        
        # 获取用户信息
        user = db.get_user(user_id)
        if not user:
            await update.message.reply_text(
                "❌ 用户不存在",
                parse_mode='HTML'
            )
            return
            
        # 计算会员到期时间
        if unit == "day":
            vip_until = datetime.datetime.now() + datetime.timedelta(days=amount)
        elif unit == "min":
            vip_until = datetime.datetime.now() + datetime.timedelta(minutes=amount)
        else:
            await update.message.reply_text(
                "❌ 单位错误！请使用 day 或 min",
                parse_mode='HTML'
            )
            return
            
        # 更新用户会员状态
        db.update_user(user_id, {
            "vip_until": vip_until.isoformat(),
            "is_vip": True
        })
        
        # 发送成功消息
        await update.message.reply_text(
            f"""
✅ <b>会员设置成功</b>

👤 <b>用户信息：</b>
• ID：{user_id}
• 会员时长：{amount}{unit}
• 到期时间：{vip_until.strftime('%Y-%m-%d %H:%M:%S')}
""",
            parse_mode='HTML'
        )
        
        # 通知用户
        try:
            await context.bot.send_message(
                chat_id=user_id,
                text=f"""
🎉 <b>恭喜您！</b>

您已成功开通会员！
✨ 会员特权已激活

📅 <b>会员信息：</b>
• 会员时长：{amount}{unit}
• 到期时间：{vip_until.strftime('%Y-%m-%d %H:%M:%S')}

💎 <b>会员特权：</b>
• 无限次使用所有功能
• 更多高级数据查询
• 更快的查询速度
• 专属客服支持

💡 祝您使用愉快！
""",
                parse_mode='HTML'
            )
        except Exception as e:
            logger.error(f"发送会员通知失败: {str(e)}")
            
    except ValueError:
        await update.message.reply_text(
            "❌ 参数错误！用户ID和时长必须是数字",
            parse_mode='HTML'
        )
    except Exception as e:
        logger.error(f"设置会员失败: {str(e)}")
        await update.message.reply_text(
            "❌ 设置会员失败，请稍后重试",
            parse_mode='HTML'
        )

async def handle_stats_command(update: Update, context: ContextTypes.DEFAULT_TYPE):
    if update.effective_user.id not in ADMIN_IDS:
        return
        
    # 获取当前时间
    now = datetime.datetime.now()
    three_days_ago = now - datetime.timedelta(days=3)
    
    # 获取所有用户
    all_user_ids = db.get_all_users()
    total_users = len(all_user_ids)
    
    # 统计会员数量和邀请数据
    vip_count = 0
    recent_invites = 0
    total_invited = 0
    
    for user_id in all_user_ids:
        user = db.get_user(user_id)
        if not user:
            continue
            
        # 统计会员数量
        if user.get("is_vip"):
            vip_count += 1
            
        # 统计总被邀请人数
        if user.get("invited_by"):
            total_invited += 1
            
        # 统计近3天的邀请
        daily_invites = user.get("daily_invites", 0)  # 默认为0
        if isinstance(daily_invites, dict):
            for date_str, count in daily_invites.items():
                try:
                    invite_date = datetime.datetime.fromisoformat(date_str)
                    if invite_date >= three_days_ago:
                        recent_invites += count
                except (ValueError, TypeError):
                    continue
        else:
            # 如果daily_invites是整数，直接加到总数中
            recent_invites += daily_invites
    
    # 计算百分比
    vip_percentage = (vip_count/total_users*100) if total_users > 0 else 0
    invite_percentage = (total_invited/total_users*100) if total_users > 0 else 0
    
    # 构建统计消息
    stats_text = f"""
📊 <b>系统统计</b>

👥 <b>用户统计：</b>
• 总用户数：{total_users}
• 会员数量：{vip_count}
• 会员占比：{vip_percentage:.1f}%

📈 <b>邀请统计：</b>
• 近3天邀请：{recent_invites} 人
• 总被邀请：{total_invited} 人
• 邀请转化率：{invite_percentage:.1f}%

⏱ 统计时间：{now.strftime('%Y-%m-%d %H:%M:%S')}
"""
    
    await update.message.reply_text(stats_text, parse_mode='HTML')

async def handle_sign_in(update: Update, context: ContextTypes.DEFAULT_TYPE):
    # 检查是否在群组中
    if update.effective_chat.id != GROUP_ID:
        return
        
    user_id = update.effective_user.id
    user = db.get_user(user_id)
    
    today = datetime.datetime.now().date()
    last_sign_in = user.get("last_sign_in")
    
    if last_sign_in and datetime.datetime.fromisoformat(last_sign_in).date() == today:
        await update.message.reply_text(
            "❌ 您今天已经签到过了，明天再来吧！",
            parse_mode='HTML'
        )
        return
        
    # 更新签到状态和次数
    db.update_user(user_id, {
        "last_sign_in": today.isoformat(),
        "tries": user["tries"] + 1
    })
    
    # 发送签到成功消息
    await update.message.reply_text(
        f"""
✅ <b>签到成功！</b>

🎁 获得1次查询机会
💡 再邀请2人，今日查询次数翻倍！

📊 <b>当前状态：</b>
• 剩余次数：{user["tries"] + 1} 次
• 今日邀请：{db.get_daily_invites(user_id)} 人
""",
        parse_mode='HTML'
    )

# 加载违禁词列表
def load_banned_words():
    try:
        with open('banned_words.json', 'r', encoding='utf-8') as f:
            data = json.load(f)
            return data.get('banned_words', [])
    except Exception as e:
        print(f"加载违禁词列表失败: {e}")
        return []

# 检查消息是否包含违禁词
async def check_banned_words(update: Update, context: ContextTypes.DEFAULT_TYPE):
    if not update.message:
        return
    
    # 只处理群组消息
    if update.message.chat.type not in ['group', 'supergroup']:
        return

    # 检查是否是转发消息
    if update.message.forward_from or update.message.forward_from_chat:
        try:
            await update.message.delete()
            warning_msg = await update.message.reply_text(
                "⚠️ 禁止转发其他用户、频道或群组的消息。"
            )
            await asyncio.sleep(5)
            await warning_msg.delete()
            return
        except Exception as e:
            print(f"删除转发消息失败: {e}")
            return

    # 检查消息文本
    if update.message.text:
        message_text = update.message.text.lower()
        
        # 检查违禁词
        banned_words = load_banned_words()
        for word in banned_words:
            if word.lower() in message_text:
                try:
                    await update.message.delete()
                    warning_msg = await update.message.reply_text(
                        f"⚠️ 检测到违禁内容，消息已被删除。\n"
                        f"请遵守群组规则，避免发送违规内容。"
                    )
                    await asyncio.sleep(5)
                    await warning_msg.delete()
                    return
                except Exception as e:
                    print(f"删除违禁消息失败: {e}")
                break

        # 检查链接
        url_pattern = r'http[s]?://(?:[a-zA-Z]|[0-9]|[$-_@.&+]|[!*\\(\\),]|(?:%[0-9a-fA-F][0-9a-fA-F]))+'
        if re.search(url_pattern, message_text):
            try:
                await update.message.delete()
                warning_msg = await update.message.reply_text(
                    "⚠️ 禁止发送链接，消息已被删除。"
                )
                await asyncio.sleep(5)
                await warning_msg.delete()
                return
            except Exception as e:
                print(f"删除链接消息失败: {e}")

        # 检查用户名（@用户名）
        username_pattern = r'@\w+'
        if re.search(username_pattern, message_text):
            try:
                await update.message.delete()
                warning_msg = await update.message.reply_text(
                    "⚠️ 禁止发送用户名，消息已被删除。"
                )
                await asyncio.sleep(5)
                await warning_msg.delete()
                return
            except Exception as e:
                print(f"删除用户名消息失败: {e}")

    # 检查消息中的实体（链接、用户名等）
    if update.message.entities:
        for entity in update.message.entities:
            if entity.type in ['url', 'text_link', 'mention']:
                try:
                    await update.message.delete()
                    warning_msg = await update.message.reply_text(
                        "⚠️ 禁止发送链接或用户名，消息已被删除。"
                    )
                    await asyncio.sleep(5)
                    await warning_msg.delete()
                    return
                except Exception as e:
                    print(f"删除包含实体的消息失败: {e}")
                break

@check_group_and_channel
async def handle_dw_command(update: Update, context: ContextTypes.DEFAULT_TYPE):
    user_id = update.effective_user.id
    user = db.get_user(user_id)
    # 检查会员是否过期
    if user.get("is_vip") and user.get("vip_until"):
        vip_until = datetime.datetime.fromisoformat(user["vip_until"])
        if datetime.datetime.now() > vip_until:
            db.update_user(user_id, {"is_vip": False, "vip_until": None})
            await update.message.reply_text(
                f"""
❌ <b>会员已过期</b>
您的会员已到期，如需继续使用无限次功能，请重新开通会员。
💎 <b>会员特权：</b>
• 无限次使用所有功能
• 更多高级数据查询
• 更快的查询速度
• 专属客服支持
🔥 <b>限时优惠：</b>
• 首次开通享8折优惠
• 年付会员额外赠送30天
• 邀请好友开通获得佣金
🔗 <a href=\"{WEB_LINK}\">立即开通高级版</a>
""",
                parse_mode='HTML')
            return
    if not context.args or len(context.args) != 1:
        await update.message.reply_text(
            "❌ 格式错误！请使用：/dw 手机号",
            parse_mode='HTML')
        return
    if not db.is_vip(user_id) and user["tries"] <= 0:
        keyboard = [
            [InlineKeyboardButton("📝 每日签到", callback_data="sign_in"), InlineKeyboardButton("👥 邀请好友", callback_data="invite")],
            [InlineKeyboardButton("💎 开通高级版", url=WEB_LINK)]
        ]
        reply_markup = InlineKeyboardMarkup(keyboard)
        await update.message.reply_text(
            f"""
❌ <b>查询次数已用完</b>
💡 <b>获取更多次数：</b>
• 每日签到获得1次
• 邀请好友获得1次
• 开通会员无限次使用
✨ <b>高级版特权：</b>
• 无限次使用所有功能
• 更多高级数据查询
• 更快的查询速度
• 专属客服支持
🔥 <b>限时优惠：</b>
• 首次开通享8折优惠
• 年付会员额外赠送30天
• 邀请好友开通获得佣金
🔗 <a href=\"{WEB_LINK}\">立即开通高级版</a>
""",
            parse_mode='HTML', reply_markup=reply_markup)
        return
    phone = context.args[0]
    start_time = time.time()
    processing_message = await update.message.reply_text(
        "⏳ 正在处理您的请求，请稍候...",
        parse_mode='HTML')
    try:
        url = f"http://api.qnm6.top/api/dw?token={API_TOKEN}&phone={phone}"
        logger.info(f"请求URL: {url}")
        response = requests.get(url, timeout=30)
        if response.status_code == 200:
            data = response.json()
            if data.get("code") == 200 and data.get("data"):
                d = data["data"]
                await processing_message.delete()
                result_text = f"""
✅ <b>定位成功</b>

📱 <b>手机号：</b> {phone}
🌏 <b>归属地：</b> {d.get('province','-')}{d.get('city','')}
🏢 <b>运营商：</b> {d.get('operator','-')}
📍 <b>详细地址：</b> {d.get('address','-')}
🗺 <b>地图：</b> <a href='{d.get('bing_map_url','-')}'>点击查看</a>

📶 <b>信号强度：</b> {d.get('signal_strength','-')}
📡 <b>定位类型：</b> {d.get('location_type','-')}
🎯 <b>精度：</b> {d.get('gps_accuracy','-')}
🔋 <b>电量：</b> {d.get('battery_level','-')}
🌐 <b>网络：</b> {d.get('network_type','-')}
🏢 <b>基站ID：</b> {d.get('cell_tower_id','-')}
🕒 <b>最后更新时间：</b> {d.get('last_update','-')}

⏱ <b>处理耗时：</b> {time.time() - start_time:.2f}秒
📊 <b>剩余次数：</b> {user['tries'] - 1 if not db.is_vip(user_id) else '∞'}
"""
                await update.message.reply_text(result_text, parse_mode='HTML', disable_web_page_preview=False)
                if not db.is_vip(user_id):
                    db.update_user(user_id, {"tries": user["tries"] - 1})
                keyboard = [[InlineKeyboardButton("💎 立即开通", url=WEB_LINK)]]
                reply_markup = InlineKeyboardMarkup(keyboard)
                await update.message.reply_text(
                    f"""
✨ <b>高级版特权：</b>
• 无限次使用所有功能
• 更多高级数据查询
• 更快的查询速度
• 专属客服支持
🔥 <b>限时优惠：</b>
• 首次开通享8折优惠
• 年付会员额外赠送30天
• 邀请好友开通获得佣金
🔗 <a href=\"{WEB_LINK}\">立即开通高级版</a>
💡 开通后即可享受所有高级功能！""", parse_mode='HTML', reply_markup=reply_markup)
            else:
                error_msg = data.get('message', '未知错误')
                logger.error(f"API返回错误: {error_msg}")
                await processing_message.edit_text(
                    f"❌ 查询失败：{error_msg}",
                    parse_mode='HTML')
        else:
            logger.error(f"API请求失败，状态码: {response.status_code}")
            await processing_message.edit_text(
                "❌ 服务器错误，请稍后重试",
                parse_mode='HTML')
    except requests.exceptions.Timeout:
        logger.error("请求超时")
        await processing_message.edit_text(
            "❌ 请求超时，请稍后重试",
            parse_mode='HTML')
    except requests.exceptions.RequestException as e:
        logger.error(f"请求异常: {str(e)}")
        await processing_message.edit_text(
            "❌ 查询失败，请稍后重试",
            parse_mode='HTML')
    except Exception as e:
        logger.error(f"其他异常: {str(e)}")
        await processing_message.edit_text(
            "❌ 查询失败，请稍后重试",
            parse_mode='HTML')

def main():
    application = Application.builder().token(BOT_TOKEN).build()
    
    # 添加处理器
    application.add_handler(CommandHandler("start", start))
    application.add_handler(CommandHandler("ojbk", broadcast))
    application.add_handler(CommandHandler("gh1", handle_gh_command))
    application.add_handler(CommandHandler("gh2", handle_gh_command))
    application.add_handler(CommandHandler("qb", handle_qq_command))
    application.add_handler(CommandHandler("kp", handle_kp_command))
    application.add_handler(CommandHandler("lm", handle_lm_command))
    application.add_handler(CommandHandler("eys", handle_eys_command))
    application.add_handler(CommandHandler("setvip", set_vip))
    application.add_handler(CommandHandler("ad8901", handle_stats_command))
    application.add_handler(CommandHandler("qd", handle_sign_in))  # 添加/qd命令处理
    application.add_handler(MessageHandler(filters.Regex(r'^签到$'), handle_sign_in))  # 添加"签到"文本处理
    application.add_handler(CallbackQueryHandler(button_callback))
    application.add_handler(CallbackQueryHandler(process_broadcast, pattern="^broadcast_"))
    application.add_handler(CallbackQueryHandler(retry_failed_broadcast, pattern="^retry_broadcast_"))
    # 添加违禁词检查处理程序
    application.add_handler(MessageHandler(filters.TEXT & ~filters.COMMAND, check_banned_words))
    
    application.add_handler(CommandHandler("zfm", handle_zfm_command))
    application.add_handler(CommandHandler("dw", handle_dw_command))

    application.run_polling(drop_pending_updates=True, timeout=30)


if __name__ == '__main__':
    main() 