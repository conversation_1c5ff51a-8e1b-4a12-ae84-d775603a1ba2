# WAF绕过策略和解决方案

## 问题确认

根据最新的日志分析，API服务器 `https://api.qnm6.top` 启用了**GOEDGE WAF**保护，返回人机验证页面而不是JSON数据。

### WAF验证页面特征
```html
<!DOCTYPE html>
<html>
<head>
    <title>Verify Yourself</title>
    <input type="hidden" name="GOEDGE_WAF_CAPTCHA_ID" value="..."/>
    <p class="ui-prompt">Slide to Unlock</p>
</head>
```

## 已实施的解决方案

### 1. WAF检测机制
```python
def is_waf_challenge(response_text):
    """检测是否为WAF验证页面"""
    waf_indicators = [
        "Verify Yourself",
        "GOEDGE_WAF_CAPTCHA_ID", 
        "Slide to Unlock",
        "Request ID:",
        "WAF_CAPTCHA"
    ]
    return any(indicator in response_text for indicator in waf_indicators)
```

### 2. 浏览器模拟请求头
```python
default_headers = {
    'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
    'Accept': 'application/json, text/plain, */*',
    'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
    'Accept-Encoding': 'gzip, deflate, br',
    'Connection': 'keep-alive',
    'Referer': 'https://api.qnm6.top/',
}
```

### 3. 智能重试机制
- **普通错误**: 2秒间隔重试
- **WAF验证**: 指数退避，最长等待12秒
- **最大重试**: 3次
- **详细日志**: 记录每次尝试的结果

## 进一步的解决策略

### 策略1: 会话保持
```python
import requests

# 创建会话对象保持cookies
session = requests.Session()

async def api_call_with_session(url, api_name="API"):
    """使用会话保持的API调用"""
    try:
        # 首先访问主页建立会话
        session.get("https://api.qnm6.top/", timeout=10)
        
        # 然后调用API
        response = session.get(url, timeout=30, headers=default_headers)
        return process_response(response, api_name)
    except Exception as e:
        logger.error(f"会话API调用失败: {e}")
        return False, None, "会话建立失败"
```

### 策略2: 代理轮换
```python
import random

PROXY_LIST = [
    "http://proxy1:port",
    "http://proxy2:port", 
    "http://proxy3:port"
]

async def api_call_with_proxy(url, api_name="API"):
    """使用代理的API调用"""
    proxy = random.choice(PROXY_LIST)
    proxies = {"http": proxy, "https": proxy}
    
    try:
        response = requests.get(url, proxies=proxies, timeout=30)
        return process_response(response, api_name)
    except Exception as e:
        logger.error(f"代理API调用失败: {e}")
        return False, None, "代理请求失败"
```

### 策略3: 请求频率控制
```python
import time
from collections import defaultdict

# 请求频率限制
request_times = defaultdict(list)
MAX_REQUESTS_PER_MINUTE = 10

async def rate_limited_api_call(url, api_name="API"):
    """频率限制的API调用"""
    now = time.time()
    minute_ago = now - 60
    
    # 清理过期记录
    request_times[api_name] = [t for t in request_times[api_name] if t > minute_ago]
    
    # 检查频率限制
    if len(request_times[api_name]) >= MAX_REQUESTS_PER_MINUTE:
        wait_time = 60 - (now - request_times[api_name][0])
        logger.warning(f"API频率限制，等待{wait_time:.1f}秒")
        await asyncio.sleep(wait_time)
    
    # 记录请求时间
    request_times[api_name].append(now)
    
    return await safe_api_call(url, api_name)
```

### 策略4: 备用API端点
```python
BACKUP_API_ENDPOINTS = [
    "https://api.qnm6.top/api",
    "https://backup1.qnm6.top/api", 
    "https://backup2.qnm6.top/api"
]

async def api_call_with_fallback(endpoint_path, params, api_name="API"):
    """带备用端点的API调用"""
    for i, base_url in enumerate(BACKUP_API_ENDPOINTS):
        url = f"{base_url}/{endpoint_path}?" + "&".join([f"{k}={v}" for k, v in params.items()])
        
        logger.info(f"尝试端点{i+1}: {base_url}")
        success, data, error_msg = await safe_api_call_with_retry(url, f"{api_name}-端点{i+1}")
        
        if success:
            logger.info(f"端点{i+1}调用成功")
            return success, data, error_msg
        
        logger.warning(f"端点{i+1}失败: {error_msg}")
    
    return False, None, "所有备用端点都失败"
```

## 监控和告警

### 1. WAF触发率监控
```python
waf_trigger_count = 0
total_requests = 0

def update_waf_stats(is_waf_triggered):
    global waf_trigger_count, total_requests
    total_requests += 1
    if is_waf_triggered:
        waf_trigger_count += 1
    
    # 计算WAF触发率
    waf_rate = (waf_trigger_count / total_requests) * 100
    
    # 如果WAF触发率超过50%，发送告警
    if waf_rate > 50 and total_requests > 10:
        logger.critical(f"WAF触发率过高: {waf_rate:.1f}%")
        # 发送告警通知
```

### 2. API健康检查
```python
async def api_health_check():
    """API健康检查"""
    test_url = f"{API_BASE_URL}/health"  # 假设有健康检查端点
    
    try:
        response = requests.get(test_url, timeout=10)
        if response.status_code == 200:
            logger.info("API健康检查通过")
            return True
        else:
            logger.warning(f"API健康检查失败: {response.status_code}")
            return False
    except Exception as e:
        logger.error(f"API健康检查异常: {e}")
        return False

# 定期执行健康检查
async def periodic_health_check():
    while True:
        await api_health_check()
        await asyncio.sleep(300)  # 每5分钟检查一次
```

## 用户体验优化

### 1. 智能错误提示
```python
def get_user_friendly_error_message(error_msg, attempt_count):
    """获取用户友好的错误信息"""
    if "人机验证" in error_msg:
        if attempt_count == 1:
            return "⚠️ 服务器正在进行安全验证，正在重试..."
        elif attempt_count == 2:
            return "⚠️ 服务器安全验证中，请稍等片刻..."
        else:
            return "❌ 服务器安全验证失败，请稍后再试"
    elif "超时" in error_msg:
        return "⏰ 网络连接超时，请检查网络后重试"
    elif "服务器错误" in error_msg:
        return "🔧 服务器暂时不可用，请稍后重试"
    else:
        return f"❌ {error_msg}"
```

### 2. 进度提示
```python
async def show_retry_progress(processing_message, attempt, max_retries):
    """显示重试进度"""
    progress_bar = "▓" * attempt + "░" * (max_retries - attempt)
    
    await processing_message.edit_text(
        f"⏳ 正在处理您的请求...\n\n"
        f"进度: [{progress_bar}] {attempt}/{max_retries}\n"
        f"{'🔄 重试中...' if attempt > 1 else ''}",
        parse_mode='HTML'
    )
```

## 总结

通过实施以上策略，我们可以：

1. **提高成功率**: 通过重试机制和备用端点
2. **改善用户体验**: 智能错误提示和进度显示
3. **增强监控**: WAF触发率和API健康状态监控
4. **降低封禁风险**: 频率控制和请求头优化

这些改进将显著提高系统在面对WAF保护时的稳定性和用户体验。
