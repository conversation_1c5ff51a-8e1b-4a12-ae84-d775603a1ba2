/**
 * API交互模块
 * 处理与后端API的所有交互
 */

class ApiService {
    constructor() {
        this.baseUrl = API_CONFIG.BASE_URL;
        this.timeout = API_CONFIG.TIMEOUT;
    }

    /**
     * 通用API调用方法
     * @param {string} endpoint - API端点
     * @param {Object} params - 请求参数
     * @param {string} method - 请求方法
     * @returns {Promise<Object>} API响应
     */
    async callApi(endpoint, params = {}, method = 'GET') {
        try {
            const url = new URL(endpoint, this.baseUrl);
            
            // 构建请求配置
            const config = {
                method: method,
                headers: {
                    'Content-Type': 'application/json',
                },
                timeout: this.timeout
            };

            // 处理参数
            if (method === 'GET') {
                // GET请求将参数添加到URL
                Object.keys(params).forEach(key => {
                    if (params[key] !== null && params[key] !== undefined) {
                        url.searchParams.append(key, params[key]);
                    }
                });
            } else {
                // POST请求将参数添加到body
                config.body = JSON.stringify(params);
            }

            // 发送请求
            const response = await fetch(url.toString(), config);
            
            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }

            const data = await response.json();
            return data;

        } catch (error) {
            console.error('API调用失败:', error);
            throw new Error(ERROR_MESSAGES.API_ERROR);
        }
    }

    /**
     * 生成商户秘钥
     * @param {string} merchantId - 商户ID
     * @returns {string} 商户秘钥
     */
    generateMerchantSecret(merchantId) {
        const key = MERCHANT_CONFIG.SECRET_KEY;
        const salt = MERCHANT_CONFIG.SALT;
        
        // 组合商户ID、密钥和盐值
        const combined = merchantId + key + salt;
        
        // 使用简单的哈希算法（在实际应用中应使用更安全的方法）
        const hash = this.simpleHash(combined);
        
        // 添加商户ID前缀
        const prefix = merchantId.substring(0, 3);
        
        return `${prefix}_${hash}`;
    }

    /**
     * 简单哈希函数（仅用于演示，实际应用中应使用crypto库）
     * @param {string} str - 要哈希的字符串
     * @returns {string} 哈希值
     */
    simpleHash(str) {
        let hash = 0;
        for (let i = 0; i < str.length; i++) {
            const char = str.charCodeAt(i);
            hash = ((hash << 5) - hash) + char;
            hash = hash & hash; // 转换为32位整数
        }
        return Math.abs(hash).toString(16).padStart(8, '0');
    }

    /**
     * 获取商户信息
     * @param {string} merchantId - 商户ID
     * @returns {Promise<Object>} 商户信息
     */
    async getMerchantInfo(merchantId) {
        if (!merchantId) {
            throw new Error(ERROR_MESSAGES.INVALID_PARAMS);
        }

        const response = await this.callApi(API_CONFIG.ENDPOINTS.GET_MERCHANT_INFO, {
            merchant_id: merchantId
        });

        if (!response || response.status !== 'success') {
            throw new Error(ERROR_MESSAGES.MERCHANT_NOT_FOUND);
        }

        return response.data;
    }

    /**
     * 注册商户
     * @param {string} merchantId - 商户ID
     * @returns {Promise<Object>} 注册结果
     */
    async registerMerchant(merchantId) {
        if (!merchantId) {
            throw new Error(ERROR_MESSAGES.INVALID_PARAMS);
        }

        const response = await this.callApi(API_CONFIG.ENDPOINTS.REGISTER_MERCHANT, {
            merchant_id: merchantId
        });

        return response;
    }

    /**
     * 获取商品列表
     * @param {string} merchantSecret - 商户秘钥
     * @returns {Promise<Array>} 商品列表
     */
    async getProductList(merchantSecret) {
        if (!merchantSecret) {
            throw new Error(ERROR_MESSAGES.INVALID_PARAMS);
        }

        const response = await this.callApi(API_CONFIG.ENDPOINTS.GET_PRODUCT_LIST, {
            merchant_secret: merchantSecret
        });

        if (!response || response.status !== 'success') {
            return [];
        }

        return response.data || [];
    }

    /**
     * 获取商品信息
     * @param {string} productId - 商品ID
     * @returns {Promise<Object>} 商品信息
     */
    async getProductInfo(productId) {
        if (!productId) {
            throw new Error(ERROR_MESSAGES.INVALID_PARAMS);
        }

        const response = await this.callApi(API_CONFIG.ENDPOINTS.GET_PRODUCT_INFO, {
            product_id: productId
        });

        if (!response || response.status !== 'success') {
            throw new Error(ERROR_MESSAGES.PRODUCT_NOT_FOUND);
        }

        return response.data;
    }

    /**
     * 创建订单
     * @param {string} customerContact - 客户联系方式
     * @param {string} productId - 商品ID
     * @param {string} payType - 支付方式
     * @returns {Promise<Object>} 订单信息
     */
    async createOrder(customerContact, productId, payType = 'wxpay') {
        if (!customerContact || !productId) {
            throw new Error(ERROR_MESSAGES.INVALID_PARAMS);
        }

        const response = await this.callApi(API_CONFIG.ENDPOINTS.CREATE_ORDER, {
            customer_contact: customerContact,
            product_id: productId,
            pay_type: payType
        });

        if (!response || response.status !== 'success') {
            const errorMessage = response?.message || ERROR_MESSAGES.ORDER_CREATE_FAILED;
            throw new Error(errorMessage);
        }

        return response.data;
    }

    /**
     * 检查支付状态
     * @param {string} orderId - 订单ID
     * @returns {Promise<Object>} 支付状态信息
     */
    async checkPaymentStatus(orderId) {
        if (!orderId) {
            throw new Error(ERROR_MESSAGES.INVALID_PARAMS);
        }

        const response = await this.callApi(API_CONFIG.ENDPOINTS.CHECK_PAYMENT_STATUS, {
            order_id: orderId
        });

        if (!response || response.status !== 'success') {
            throw new Error(ERROR_MESSAGES.PAYMENT_CHECK_FAILED);
        }

        return response.data;
    }

    /**
     * 设置订单支付状态
     * @param {string} orderId - 订单ID
     * @param {string} paymentStatus - 支付状态
     * @returns {Promise<Object>} 设置结果
     */
    async setOrderPaymentStatus(orderId, paymentStatus) {
        if (!orderId || !paymentStatus) {
            throw new Error(ERROR_MESSAGES.INVALID_PARAMS);
        }

        const response = await this.callApi(API_CONFIG.ENDPOINTS.SET_ORDER_PAYMENT_STATUS, {
            order_id: orderId,
            payment_status: paymentStatus
        });

        return response;
    }

    /**
     * 获取所有商户列表（模拟数据，实际需要后端支持）
     * @returns {Promise<Array>} 商户列表
     */
    async getAllMerchants() {
        // 这里应该调用实际的API，目前返回模拟数据
        return new Promise((resolve) => {
            setTimeout(() => {
                resolve([
                    {
                        merchant_id: '123456',
                        shop_name: '示例商店1',
                        shop_description: '这是一个示例商店，提供各种优质商品。',
                        product_count: 15
                    },
                    {
                        merchant_id: '789012',
                        shop_name: '示例商店2',
                        shop_description: '专业的数字商品销售平台。',
                        product_count: 8
                    }
                ]);
            }, 500);
        });
    }

    /**
     * 搜索商品或商店
     * @param {string} keyword - 搜索关键词
     * @returns {Promise<Object>} 搜索结果
     */
    async search(keyword) {
        if (!keyword || keyword.trim() === '') {
            return { shops: [], products: [] };
        }

        // 这里应该调用实际的搜索API，目前返回模拟数据
        return new Promise((resolve) => {
            setTimeout(() => {
                resolve({
                    shops: [
                        {
                            merchant_id: '123456',
                            shop_name: '示例商店1',
                            shop_description: '这是一个示例商店，提供各种优质商品。'
                        }
                    ],
                    products: [
                        {
                            product_id: '1',
                            product_name: '示例商品1',
                            product_description: '这是一个示例商品。',
                            product_price: '99.00',
                            merchant_id: '123456'
                        }
                    ]
                });
            }, 300);
        });
    }
}

// 创建全局API服务实例
const apiService = new ApiService();
